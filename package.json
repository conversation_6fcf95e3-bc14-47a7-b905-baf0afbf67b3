{"name": "aisurvey-web", "version": "1.0.0", "description": "AI Survey Application with SurveyJS and Strapi", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run develop", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build": "npm run build:backend && npm run build:frontend", "test:e2e": "cd frontend && npm run cypress:run", "test:e2e:open": "cd frontend && npm run cypress:open", "setup": "npm install && cd frontend && npm install && cd ../backend && npm install", "import-data": "cd backend && npm run import-survey-data"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.5.3"}, "dependencies": {"puppeteer": "^24.9.0"}}