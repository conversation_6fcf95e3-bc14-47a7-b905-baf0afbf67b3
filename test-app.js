const puppeteer = require('puppeteer');

async function testApp() {
  console.log('🚀 Testing AI Survey Application...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1280, height: 720 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Navigate to the app
    console.log('📱 Opening http://localhost:3000...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Wait for the app to load
    await page.waitForTimeout(2000);
    
    // Take a screenshot
    await page.screenshot({ path: 'app-screenshot.png', fullPage: true });
    console.log('📸 Screenshot saved as app-screenshot.png');
    
    // Check if user info form is displayed
    const userInfoForm = await page.$('input[name="name"]');
    if (userInfoForm) {
      console.log('✅ User info form is displayed');
      
      // Fill the form
      await page.type('input[name="name"]', 'Test User');
      await page.type('input[name="email"]', '<EMAIL>');
      
      // Submit the form
      await page.click('button[type="submit"]');
      console.log('📝 Submitted user info form');
      
      // Wait for survey to load
      await page.waitForTimeout(3000);
      
      // Check if survey is displayed
      const surveyElement = await page.$('.sv-root');
      if (surveyElement) {
        console.log('✅ Survey is displayed');
        
        // Take another screenshot
        await page.screenshot({ path: 'survey-screenshot.png', fullPage: true });
        console.log('📸 Survey screenshot saved as survey-screenshot.png');
        
        // Try to answer first question
        const firstChoice = await page.$('input[type="radio"]');
        if (firstChoice) {
          await firstChoice.click();
          console.log('✅ Answered first question');
          
          // Try to complete survey
          const completeButton = await page.$('input[value="Complete"]');
          if (completeButton) {
            await completeButton.click();
            console.log('📋 Completed survey');
            
            // Wait for results
            await page.waitForTimeout(3000);
            
            // Take final screenshot
            await page.screenshot({ path: 'results-screenshot.png', fullPage: true });
            console.log('📸 Results screenshot saved as results-screenshot.png');
          }
        }
      } else {
        console.log('❌ Survey not displayed');
        const errorElement = await page.$('h2');
        if (errorElement) {
          const errorText = await page.evaluate(el => el.textContent, errorElement);
          console.log('Error message:', errorText);
        }
      }
    } else {
      console.log('❌ User info form not found');
      const pageContent = await page.content();
      console.log('Page content preview:', pageContent.substring(0, 500));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Install puppeteer if not available
const { execSync } = require('child_process');
try {
  require('puppeteer');
} catch (e) {
  console.log('Installing puppeteer...');
  execSync('npm install puppeteer', { stdio: 'inherit' });
}

testApp();
