export function ChatTextField({
  handleSubmit,
  handleInputChange,
  input,
  loading,
}: React.PropsWithChildren<{
  handleSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  handleInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  input: string;
  loading: boolean;
}>) {
  return (
    <form onSubmit={handleSubmit} className={'border-muted border-t p-4'}>
      <input
        disabled={loading}
        onInput={handleInputChange}
        value={input}
        placeholder="Ask your PDF anything and it will answer you."
        className={
          'bg-secondary/50 !min-h-[60px] w-full border px-4 shadow-sm' +
          ' focus:ring-none hover:bg-accent focus:bg-accent transition-all outline-none' +
          ' aria-disabled:opacity-50'
        }
      />
    </form>
  );
}
