import type { SupabaseClient } from '@supabase/supabase-js';

import { SupabaseHybridSearch } from '@langchain/community/retrievers/supabase';
import { SupabaseVectorStore } from '@langchain/community/vectorstores/supabase';

import { getEmbeddingsModel } from '~/lib/ai/embeddings-model';
import { Database } from '~/lib/database.types';

const SIMILARITY_K = process.env.SIMILARITY_K
  ? Number(process.env.SIMILARITY_K)
  : 2;

const KEYWORD_K = process.env.KEYWORD_K ? Number(process.env.KEYWORD_K) : 2;

/**
 * Get a vector store for storing and retrieving documents.
 * @param client
 */
export async function getVectorStore(client: SupabaseClient<Database>) {
  const embeddings = getEmbeddingsModel();

  return SupabaseVectorStore.fromExistingIndex(embeddings, {
    client,
    tableName: 'documents_embeddings',
    queryName: 'match_documents',
  });
}

/**
 * Get a vector retriever for the given document ID.
 * @param client
 * @param documentId
 */
export async function getVectorRetriever(
  client: SupabaseClient<Database>,
  documentId: string,
) {
  const embeddings = getEmbeddingsModel();

  const retriever = new SupabaseHybridSearch(embeddings, {
    client,
    similarityK: SIMILARITY_K,
    keywordK: KEYWORD_K,
    tableName: 'documents',
    similarityQueryName: 'match_documents',
    keywordQueryName: 'kw_match_documents',
  });

  retriever.metadata = { document_id: documentId };

  return retriever;
}
