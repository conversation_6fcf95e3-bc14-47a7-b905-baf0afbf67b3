create extension vector with schema extensions;

create type sender as <PERSON><PERSON><PERSON> (
    'user',
    'assistant'
    );

create table documents_embeddings
(
    id         uuid primary key default gen_random_uuid(),
    embedding  vector(1536),
    content    text                           not null,
    metadata   jsonb            default '{}'  not null,
    created_at timestamptz      default now() not null
);

grant delete on table documents_embeddings to authenticated;
grant select, delete, insert, update on table documents_embeddings to service_role;

alter table documents_embeddings
    enable row level security;

create policy documents_read
    on documents_embeddings
    for select
    to authenticated
    using (
    (metadata ->> 'account_id')::uuid = (select auth.uid())
    );

create policy documents_delete
    on documents_embeddings
    for delete
    to authenticated
    using (
    (metadata ->> 'account_id')::uuid = (select auth.uid())
    );

/*
* DOCUMENTS
*/
create table documents
(
    content    text         not null,
    summary    text         not null default '',
    title      varchar(500) not null,
    created_at timestamptz           default now() not null,
    account_id uuid         not null references public.accounts (id) on delete cascade,
    id         uuid primary key      default gen_random_uuid()
);

grant select, delete on table documents to authenticated, service_role;
grant delete, insert, update on table documents to service_role;

alter table documents
    enable row level security;

-- indexes
create index ix_documents_account_id on documents (account_id);

-- rls

create policy documents_read
    on documents
    for select
    to authenticated
    using (
    account_id = (select auth.uid())
    );

create policy documents_delete
    on documents
    for delete
    to authenticated
    using (
    account_id = (select auth.uid())
    );

/*
* CONVERSATIONS
*/
create table conversations
(
    id           bigint generated by default as identity primary key,
    reference_id varchar(100)              not null unique,
    name         varchar(500)              not null,
    document_id  uuid                      not null references public.documents on delete cascade,
    account_id   uuid                      not null references public.accounts (id) on delete cascade,
    created_at   timestamptz default now() not null
);

grant select, delete, insert, update on table documents to authenticated, service_role;

-- indexes
create index ix_conversations_account_id on conversations (account_id);
create index ix_conversations_document_id on conversations (document_id);

-- rls

alter table conversations
    enable row level security;

create policy conversations_insert
    on conversations
    for insert
    to authenticated
    with check (
    account_id = (select auth.uid())
    );

create policy conversations_read
    on conversations
    for select
    to authenticated
    using (
    account_id = (select auth.uid())
    );

create policy conversations_delete
    on conversations
    for delete
    to authenticated
    using (
    account_id = (select auth.uid())
    );

/*
* MESSAGES
*/
create table messages
(
    id              bigint generated by default as identity primary key,
    conversation_id bigint                    not null references public.conversations on delete cascade,
    account_id      uuid                      not null references public.accounts on delete cascade,
    text            varchar(2000)             not null,
    sender          sender                    not null,
    created_at      timestamptz default now() not null
);

grant select, delete, insert, update on table messages to authenticated, service_role;

-- index
create index ix_messages_conversation_id on messages (conversation_id);
create index ix_messages_account_id on messages (account_id);

-- rls
alter table messages
    enable row level security;

create policy messages_read
    on messages
    for select
    to authenticated
    using (
    account_id = (select auth.uid())
    );

create policy messages_insert
    on messages
    for insert
    to authenticated
    with check (
    account_id = (select auth.uid())
    );

create policy messages_delete
    on messages
    for delete
    to authenticated
    using (
    account_id = (select auth.uid())
    );

/*
* PLANS
*/
create table plans
(
    name          text   not null,
    variant_id    text   not null,
    max_documents bigint not null,
    tokens        bigint not null,
    primary key (variant_id)
);

grant select on table plans to authenticated, service_role;

alter table plans
    enable row level security;

create policy plans_read
    on plans
    for select
    to authenticated
    using (true);

/*
* CREDITS
*/
create table credits_usage
(
    id           bigint generated by default as identity primary key,
    account_id   uuid                  not null references public.accounts on delete cascade,
    tokens_quota bigint default 100000 not null
);

grant select on table credits_usage to authenticated;
grant select, insert, update, delete on table credits_usage to service_role;

-- indexes
create index ix_credits_usage_account_id on credits_usage (account_id);

-- rls
alter table credits_usage
    enable row level security;

create policy read_credits_usage
    on credits_usage
    for select
    to authenticated
    using (
    account_id = (select auth.uid())
    );

-- insert usage row for organizations on creation
create function public.handle_new_account_credits_usage()
    returns trigger
    language plpgsql
    security definer set search_path = public
as
$$
begin
    insert into public.credits_usage (account_id)
    values (new.id);
    return new;
end;
$$;

-- trigger the function every time a user is created
create trigger on_account_created
    after insert
    on public.accounts
    for each row
execute procedure public.handle_new_account_credits_usage();

insert into storage.buckets (id, name, PUBLIC)
values ('documents', 'documents', false);

create or replace function public.get_remaining_tokens()
    returns bigint
    set search_path = ''
as
$$
declare
    tokens_left bigint;
begin
    select tokens_quota from public.credits_usage where account_id = (select auth.uid()) into tokens_left;

    return tokens_left;
end;
$$
    language plpgsql;

grant execute on function public.get_remaining_tokens to authenticated, service_role;

create policy all_on_storage_documents on storage.objects
    for all
    to authenticated
    using (bucket_id = 'documents' and (
    auth.uid() = ((storage.foldername(name))[1]::uuid)
    ))
    with check (
    bucket_id = 'documents' and (
        auth.uid() = ((storage.foldername(name))[1]::uuid)
        )
    );

-- Create a function to similarity search for documents
create or replace function public.match_documents(
    query_embedding vector(1536),
    match_count int default null,
    filter jsonb default '{}'
)
    returns table
            (
                id         uuid,
                content    text,
                metadata   jsonb,
                similarity float
            )
as
$$
    # variable_conflict use_column
begin
    return query
        select id,
               content,
               metadata,
               1 - (public.documents_embeddings.embedding <=> query_embedding) as similarity
        from public.documents_embeddings
        where metadata @> filter
        order by public.documents_embeddings.embedding <=> query_embedding
        limit match_count;
end;
$$ language plpgsql;

-- Create a function to keyword search for documents
create or replace function public.kw_match_documents(query_text text, match_count int)
    returns table
            (
                id         uuid,
                content    text,
                metadata   jsonb,
                similarity real
            )
as
$$

begin
    return query execute
        format('select id, content, metadata, ts_rank(to_tsvector(content), plainto_tsquery($1)) as similarity
from public.documents_embeddings
where to_tsvector(content) @@ plainto_tsquery($1)
order by similarity desc
limit $2')
        using query_text, match_count;
end;
$$ language plpgsql;

grant
    execute on function public.match_documents to authenticated,
    service_role;

grant
    execute on function public.kw_match_documents to authenticated,
    service_role;

create index on public.documents_embeddings using hnsw (embedding vector_cosine_ops);