# SHARED <PERSON><PERSON><PERSON>ROMENT VARIABLES
# HERE YOU CAN ADD ALL THE **PUBLIC** ENVIRONMENT VARIABLES THAT ARE SHARED ACROSS ALL THE ENVIROMENTS
# PLEASE DO NOT ADD ANY CONFIDENTIAL KEYS OR SENSITIVE INFORMATION HERE
# ONLY CONFIGURATION, PATH, FEATURE FLAGS, ETC.
# TO OVERRIDE THESE VARIABLES IN A SPECIFIC ENVIRONMENT, PLEASE ADD THEM TO THE SPECIFIC ENVIRONMENT FILE (e.g. .env.development, .env.production)

# SITE
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_PRODUCT_NAME=Makerchat
NEXT_PUBLIC_SITE_TITLE="Makerchat - AI Support chatbots for your website"
NEXT_PUBLIC_SITE_DESCRIPTION="Makerchat is a platform that allows you to create AI chatbots for your website. Improve your customer support and increase your sales with AI chatbots."
NEXT_PUBLIC_DEFAULT_THEME_MODE=light
NEXT_PUBLIC_THEME_COLOR="#ffffff"
NEXT_PUBLIC_THEME_COLOR_DARK="#0a0a0a"

# AUTH
NEXT_PUBLIC_AUTH_PASSWORD=true
NEXT_PUBLIC_AUTH_MAGIC_LINK=false
NEXT_PUBLIC_CAPTCHA_SITE_KEY=

# BILLING
NEXT_PUBLIC_BILLING_PROVIDER=stripe

# CMS
CMS_CLIENT=keystatic

# KEYSTATIC
NEXT_PUBLIC_KEYSTATIC_CONTENT_PATH=./content

# LOCALES PATH
NEXT_PUBLIC_LOCALES_PATH=apps/web/public/locales

# FEATURE FLAGS
NEXT_PUBLIC_ENABLE_THEME_TOGGLE=true
NEXT_PUBLIC_ENABLE_PERSONAL_ACCOUNT_DELETION=true
NEXT_PUBLIC_ENABLE_PERSONAL_ACCOUNT_BILLING=false
NEXT_PUBLIC_ENABLE_TEAM_ACCOUNTS_DELETION=true
NEXT_PUBLIC_ENABLE_TEAM_ACCOUNTS_BILLING=true
NEXT_PUBLIC_ENABLE_TEAM_ACCOUNTS=true
NEXT_PUBLIC_ENABLE_TEAM_ACCOUNTS_CREATION=true
NEXT_PUBLIC_LANGUAGE_PRIORITY=application

# CHATBOT
NEXT_PUBLIC_CHATBOT_API_URL=/api/chat
NEXT_PUBLIC_WIDGET_HOSTING_URL=/dist/chatbot-widget.js

# NEXTJS
NEXT_TELEMETRY_DISABLED=1