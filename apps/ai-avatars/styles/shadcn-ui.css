/*
* shadcn-ui.css
*
* Update the below to customize your Shadcn UI CSS Colors.
* Refer to https://ui.shadcn.com/themes for applying new colors.
* NB: apply the hsl function to the colors copied from the theme.
 */
@layer base {
    :root {
        --background: hsl(0 0% 100%);
        --foreground: hsl(20 14.3% 4.1%);
        --card: hsl(0 0% 100%);
        --card-foreground: hsl(20 14.3% 4.1%);
        --popover: hsl(0 0% 100%);
        --popover-foreground: hsl(20 14.3% 4.1%);
        --primary: hsl(47.9 95.8% 53.1%);
        --primary-foreground: hsl(26 83.3% 14.1%);
        --secondary: hsl(60 4.8% 95.9%);
        --secondary-foreground: hsl(24 9.8% 10%);
        --muted: hsl(60 4.8% 95.9%);
        --muted-foreground: hsl(25 5.3% 44.7%);
        --accent: hsl(60 4.8% 95.9%);
        --accent-foreground: hsl(24 9.8% 10%);
        --destructive: hsl(0 84.2% 60.2%);
        --destructive-foreground: hsl(60 9.1% 97.8%);
        --border: hsl(20 5.9% 90%);
        --input: hsl(20 5.9% 90%);
        --ring: hsl(20 14.3% 4.1%);
        --radius: 0.75rem;
        --chart-1: hsl(12 76% 61%);
        --chart-2: hsl(173 58% 39%);
        --chart-3: hsl(197 37% 24%);
        --chart-4: hsl(43 74% 66%);
        --chart-5: hsl(27 87% 67%);

        --sidebar-background: hsl(0 0% 98%);
        --sidebar-foreground: hsl(240 5.3% 26.1%);
        --sidebar-primary: hsl(240 5.9% 10%);
        --sidebar-primary-foreground: hsl(0 0% 98%);
        --sidebar-accent: hsl(240 4.8% 95.9%);
        --sidebar-accent-foreground: hsl(240 5.9% 10%);
        --sidebar-border: hsl(220 13% 91%);
        --sidebar-ring: hsl(217.2 91.2% 59.8%);
    }

    .dark {
        --background: hsl(224 71.4% 4.1%);
        --foreground: hsl(210 20% 98%);
        --card: hsl(224 71.4% 4.1%);
        --card-foreground: hsl(210 20% 98%);
        --popover: hsl(224 71.4% 4.1%);
        --popover-foreground: hsl(210 20% 98%);
        --secondary: hsl(215 27.9% 13%);
        --secondary-foreground: hsl(210 20% 98%);
        --muted: hsl(215 27.9% 13%);
        --muted-foreground: hsl(217.9 10.6% 64.9%);
        --accent: hsl(215 27.9% 13%);
        --accent-foreground: hsl(210 20% 98%);
        --destructive: hsl(0 62.8% 30.6%);
        --destructive-foreground: hsl(210 20% 98%);
        --border: hsl(215 27.9% 13%);
        --input: hsl(215 27.9% 13%);
        --ring: hsl(216 12.2% 83.9%);

        --chart-1: hsl(220 70% 50%);
        --chart-2: hsl(160 60% 45%);
        --chart-3: hsl(30 80% 55%);
        --chart-4: hsl(280 65% 60%);
        --chart-5: hsl(340 75% 55%);

        --sidebar-background: hsl(224 71.4% 4.1%);
        --sidebar-foreground: hsl(240 4.8% 95.9%);
        --sidebar-primary: hsl(224.3 76.3% 48%);
        --sidebar-primary-foreground: hsl(0 0% 100%);
        --sidebar-accent: hsl(215 27.9% 13%);
        --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
        --sidebar-border: hsl(240 3.7% 15.9%);
        --sidebar-ring: hsl(217.2 91.2% 59.8%);
    }
}