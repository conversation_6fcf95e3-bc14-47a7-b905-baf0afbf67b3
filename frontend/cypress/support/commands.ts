/// <reference types="cypress" />

// Custom commands for the survey application

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Fill user info form
       */
      fillUserInfo(name: string, email: string): Chainable<void>
      
      /**
       * Complete survey with specific answers
       */
      completeSurvey(answers: Record<string, string>): Chainable<void>
      
      /**
       * Check if API is responding
       */
      checkApiHealth(): Chainable<void>
      
      /**
       * Wait for survey to load
       */
      waitForSurveyLoad(): Chainable<void>
    }
  }
}

Cypress.Commands.add('fillUserInfo', (name: string, email: string) => {
  cy.get('input[name="name"]').type(name)
  cy.get('input[name="email"]').type(email)
  cy.get('button[type="submit"]').click()
})

Cypress.Commands.add('completeSurvey', (answers: Record<string, string>) => {
  // Wait for survey to load
  cy.waitForSurveyLoad()
  
  // Fill answers
  Object.entries(answers).forEach(([questionName, answer]) => {
    cy.get(`input[name="${questionName}"][value="${answer}"]`).click()
  })
  
  // Submit survey
  cy.get('input[value="Complete"]').click()
})

Cypress.Commands.add('checkApiHealth', () => {
  cy.request({
    method: 'GET',
    url: `${Cypress.env('apiUrl')}/surveys`,
    failOnStatusCode: false
  }).then((response) => {
    expect(response.status).to.be.oneOf([200, 401, 403])
  })
})

Cypress.Commands.add('waitForSurveyLoad', () => {
  cy.get('.sv-root', { timeout: 10000 }).should('be.visible')
  cy.get('.sv-page__title').should('be.visible')
})
