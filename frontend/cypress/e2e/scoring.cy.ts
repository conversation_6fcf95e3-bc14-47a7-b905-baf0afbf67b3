describe('Survey Scoring System', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.fillUserInfo('Test User', '<EMAIL>')
    cy.waitForSurveyLoad()
  })

  it('should calculate correct scores for all minimum answers', () => {
    // Answer all questions with 'a' (score = 1)
    const answers: Record<string, string> = {}
    for (let i = 1; i <= 82; i++) {
      answers[`question${i}`] = 'a'
    }
    
    cy.completeSurvey(answers)
    
    // Check that all group scores are 1.00 (minimum)
    cy.get('[class*="text-2xl"][class*="font-bold"]').each(($el) => {
      cy.wrap($el).should('contain', '1.00')
    })
    
    // Should show lowest ranking
    cy.get('[class*="prose"]').should('contain', 'giai đoạn khởi đầu')
  })

  it('should calculate correct scores for all maximum answers', () => {
    // Answer all questions with 'd' (score = 4)
    const answers: Record<string, string> = {}
    for (let i = 1; i <= 82; i++) {
      answers[`question${i}`] = 'd'
    }
    
    cy.completeSurvey(answers)
    
    // Check that all group scores are 4.00 (maximum)
    cy.get('[class*="text-2xl"][class*="font-bold"]').each(($el) => {
      cy.wrap($el).should('contain', '4.00')
    })
    
    // Should show highest ranking
    cy.get('[class*="prose"]').should('contain', 'tích hợp trí tuệ nhân tạo')
  })

  it('should calculate mixed scores correctly', () => {
    // Mix of answers to test calculation
    const answers: Record<string, string> = {}
    
    // Data questions (1-11): mix of b(2) and c(3) = average 2.5
    for (let i = 1; i <= 11; i++) {
      answers[`question${i}`] = i % 2 === 0 ? 'b' : 'c'
    }
    
    // Technology questions (12-26): all c(3) = average 3.0
    for (let i = 12; i <= 26; i++) {
      answers[`question${i}`] = 'c'
    }
    
    // Other questions: all b(2) = average 2.0
    for (let i = 27; i <= 82; i++) {
      answers[`question${i}`] = 'b'
    }
    
    cy.completeSurvey(answers)
    
    // Check specific group scores
    cy.get('[class*="grid"]').within(() => {
      // Data group should be around 2.5
      cy.contains('Dữ liệu').parent().find('[class*="text-2xl"]').should('contain', '2.')
      
      // Technology group should be 3.0
      cy.contains('Công nghệ').parent().find('[class*="text-2xl"]').should('contain', '3.00')
      
      // Other groups should be 2.0
      cy.contains('Con người, Văn hóa, Tổ chức').parent().find('[class*="text-2xl"]').should('contain', '2.00')
    })
  })

  it('should preserve score mapping despite randomized choices', () => {
    // Get the first question's choices order
    cy.get('.sv-question').first().within(() => {
      cy.get('input[type="radio"]').then(($inputs) => {
        const firstChoiceValue = $inputs.first().val()
        
        // Click the first choice (regardless of its value)
        cy.wrap($inputs.first()).click()
        
        // Continue with survey
        cy.get('input[value="Complete"]').click()
        
        // The score should correspond to the actual value, not position
        // This is tested implicitly by the scoring calculation
        cy.get('[class*="text-2xl"][class*="font-bold"]').should('exist')
      })
    })
  })

  it('should handle incomplete surveys gracefully', () => {
    // Try to submit without answering required questions
    cy.get('input[value="Complete"]').click()
    
    // Should show validation errors or prevent submission
    cy.get('.sv-question--error, .sv-string-viewer').should('exist')
  })

  it('should display progress correctly', () => {
    // Check progress bar exists
    cy.get('.sv-progress').should('be.visible')
    
    // Answer first question and check progress updates
    cy.get('input[type="radio"]').first().click()
    cy.get('input[value="Next"]').click()
    
    // Progress should have increased
    cy.get('.sv-progress__bar').should('have.attr', 'style').and('include', 'width')
  })
})
