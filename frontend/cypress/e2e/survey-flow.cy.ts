describe('Survey Application Flow', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('should display user info form on initial load', () => {
    cy.get('h2').should('contain', 'Thông tin người tham gia')
    cy.get('input[name="name"]').should('be.visible')
    cy.get('input[name="email"]').should('be.visible')
    cy.get('button[type="submit"]').should('contain', 'Bắt đầu khảo sát')
  })

  it('should validate user info form', () => {
    // Test empty form
    cy.get('button[type="submit"]').click()
    cy.get('.form-error').should('exist')

    // Test invalid email
    cy.get('input[name="name"]').type('Test User')
    cy.get('input[name="email"]').type('invalid-email')
    cy.get('input[name="email"]').blur()
    cy.get('.form-error').should('contain', '<PERSON><PERSON> không hợp lệ')

    // Test valid form
    cy.get('input[name="email"]').clear().type('<EMAIL>')
    cy.get('.form-error').should('not.exist')
  })

  it('should proceed to survey after valid user info', () => {
    cy.fillUserInfo('Test User', '<EMAIL>')
    
    // Should navigate to survey
    cy.waitForSurveyLoad()
    cy.get('.sv-page__title').should('be.visible')
  })

  it('should display survey questions with randomized choices', () => {
    cy.fillUserInfo('Test User', '<EMAIL>')
    cy.waitForSurveyLoad()

    // Check that questions are displayed
    cy.get('.sv-question__title').should('have.length.greaterThan', 0)
    
    // Check that choices are displayed
    cy.get('.sv-selectbase__item').should('have.length.greaterThan', 0)
    
    // Verify that choices have the expected values (a, b, c, d)
    cy.get('input[type="radio"]').should('have.length.greaterThan', 0)
    cy.get('input[type="radio"]').first().should('have.attr', 'value').and('match', /^[a-d]$/)
  })

  it('should complete survey and show results', () => {
    cy.fillUserInfo('Test User', '<EMAIL>')
    
    // Complete survey with all 'a' answers (lowest scores)
    const answers: Record<string, string> = {}
    for (let i = 1; i <= 82; i++) {
      answers[`question${i}`] = 'a'
    }
    
    cy.completeSurvey(answers)
    
    // Should show results page
    cy.get('h1').should('contain', 'Hoàn thành khảo sát')
    cy.get('.text-green-500').should('be.visible') // Success icon
    
    // Should show scores
    cy.get('[class*="grid"]').should('contain', 'Chiến lược và Lãnh đạo')
    cy.get('[class*="grid"]').should('contain', 'Dữ liệu')
    cy.get('[class*="grid"]').should('contain', 'Công nghệ')
  })

  it('should handle API errors gracefully', () => {
    // Mock API failure
    cy.intercept('GET', '**/api/surveys*', { statusCode: 500 }).as('getSurveyError')
    
    cy.visit('/')
    
    // Should show error state
    cy.get('h2').should('contain', 'Có lỗi xảy ra')
    cy.get('button').should('contain', 'Thử lại')
  })

  it('should restart survey when requested', () => {
    cy.fillUserInfo('Test User', '<EMAIL>')
    
    // Complete survey quickly
    const answers: Record<string, string> = { question1: 'a' }
    cy.completeSurvey(answers)
    
    // Click restart
    cy.get('button').contains('Làm lại khảo sát').click()
    
    // Should return to user info form
    cy.get('h2').should('contain', 'Thông tin người tham gia')
  })
})
