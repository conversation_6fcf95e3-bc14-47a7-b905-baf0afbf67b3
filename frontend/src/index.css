@tailwind base;
@tailwind components;
@tailwind utilities;

/* SurveyJS custom styles */
.sv-root {
  @apply font-sans;
}

.sv-page__title {
  @apply text-2xl font-bold text-gray-800 mb-6;
}

.sv-question__title {
  @apply text-lg font-semibold text-gray-700 mb-3;
}

.sv-question__description {
  @apply text-sm text-gray-600 mb-4 italic;
}

.sv-selectbase__item {
  @apply mb-2;
}

.sv-selectbase__label {
  @apply flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors;
}

.sv-selectbase__label:hover {
  @apply border-primary-300 bg-primary-50;
}

.sv-selectbase__control {
  @apply mr-3;
}

.sv-string-viewer {
  @apply text-gray-800;
}

/* Custom button styles */
.sv-btn {
  @apply px-6 py-2 rounded-lg font-medium transition-colors;
}

.sv-btn--navigation {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}

.sv-btn--navigation:disabled {
  @apply bg-gray-300 text-gray-500 cursor-not-allowed;
}

/* Progress bar */
.sv-progress {
  @apply w-full bg-gray-200 rounded-full h-2 mb-6;
}

.sv-progress__bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}

/* Form styles */
.user-info-form {
  @apply max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
}

.form-input.error {
  @apply border-red-500 focus:ring-red-500;
}

.form-error {
  @apply text-red-500 text-sm mt-1;
}

.form-button {
  @apply w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors;
}

.form-button:disabled {
  @apply bg-gray-300 cursor-not-allowed;
}
