import { z } from 'zod';

// User info validation schema
export const userInfoSchema = z.object({
  name: z.string()
    .min(1, 'Tên không được để trống')
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(100, 'Tên không được quá 100 ký tự')
    .regex(/^[a-zA-ZÀ-ỹ\s]+$/, 'Tên chỉ được chứa chữ cái và khoảng trắng'),
  
  email: z.string()
    .min(1, 'Email không được để trống')
    .email('Email không hợp lệ')
    .max(255, 'Email không được quá 255 ký tự')
    .toLowerCase()
});

export type UserInfoFormData = z.infer<typeof userInfoSchema>;

// Validation helper functions
export const validateUserInfo = (data: unknown) => {
  return userInfoSchema.safeParse(data);
};

export const getValidationErrors = (error: z.ZodError) => {
  const errors: Record<string, string> = {};
  
  error.errors.forEach((err) => {
    if (err.path.length > 0) {
      errors[err.path[0] as string] = err.message;
    }
  });
  
  return errors;
};
