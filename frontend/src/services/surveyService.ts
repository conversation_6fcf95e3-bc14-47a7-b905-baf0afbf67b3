import { shuffle } from 'lodash';
import { Survey, SurveyJSModel, SurveyJSPage, SurveyJSQuestion, SurveyJSChoice, CalculatedValue } from '../types/survey';

export class SurveyService {
  // Convert Strapi survey data to SurveyJS format
  static convertToSurveyJS(survey: Survey): SurveyJSModel {
    const pages: SurveyJSPage[] = survey.questionGroups.map(group => ({
      name: group.name,
      title: group.title,
      elements: this.convertQuestionsToSurveyJS(group)
    }));

    return {
      locale: survey.locale,
      title: survey.title,
      description: survey.description,
      completedHtml: survey.completedHtml,
      completedHtmlOnCondition: survey.completedHtmlOnCondition,
      pages,
      calculatedValues: survey.calculatedValues
    };
  }

  // Convert questions from all subgroups to SurveyJS format
  private static convertQuestionsToSurveyJS(group: any): SurveyJSQuestion[] {
    const questions: SurveyJSQuestion[] = [];

    group.subGroups.forEach((subGroup: any) => {
      subGroup.questions.forEach((question: any) => {
        const surveyJSQuestion: SurveyJSQuestion = {
          type: question.type,
          name: question.name,
          title: question.title,
          description: question.description ? `${subGroup.name} - ${question.description}` : subGroup.name,
          isRequired: question.isRequired,
          choices: this.shuffleChoices(question.choices)
        };

        questions.push(surveyJSQuestion);
      });
    });

    return questions;
  }

  // Shuffle choices while preserving score mapping
  private static shuffleChoices(choices: any[]): SurveyJSChoice[] {
    const shuffledChoices = shuffle([...choices]);

    return shuffledChoices.map(choice => ({
      value: choice.value,
      text: choice.text
    }));
  }

  // Calculate scores from survey results
  static calculateScores(surveyData: Record<string, any>, originalSurvey: Survey): Record<string, number> {
    const scores: Record<string, number> = {};

    // Create a mapping of question name to choices for score lookup
    const questionChoicesMap = new Map<string, Map<string, number>>();

    originalSurvey.questionGroups.forEach(group => {
      group.subGroups.forEach(subGroup => {
        subGroup.questions.forEach(question => {
          const choicesMap = new Map<string, number>();
          question.choices.forEach(choice => {
            choicesMap.set(choice.value, choice.score);
          });
          questionChoicesMap.set(question.name, choicesMap);
        });
      });
    });

    // Calculate individual question scores
    Object.entries(surveyData).forEach(([questionName, answer]) => {
      if (questionChoicesMap.has(questionName) && answer) {
        const choicesMap = questionChoicesMap.get(questionName)!;
        const score = choicesMap.get(answer as string) || 0;
        scores[`${questionName}Score`] = score;
      }
    });

    // Calculate group averages based on the survey structure
    this.calculateGroupScores(scores, originalSurvey);

    return scores;
  }

  // Calculate group average scores
  private static calculateGroupScores(scores: Record<string, number>, survey: Survey) {
    // Calculate group scores based on actual survey structure
    survey.questionGroups.forEach(group => {
      const groupQuestions: string[] = [];

      group.subGroups.forEach(subGroup => {
        subGroup.questions.forEach(question => {
          groupQuestions.push(question.name);
        });
      });

      if (groupQuestions.length > 0) {
        let totalScore = 0;
        let questionCount = 0;

        groupQuestions.forEach(questionName => {
          const questionScore = scores[`${questionName}Score`];
          if (questionScore !== undefined) {
            totalScore += questionScore;
            questionCount++;
          }
        });

        if (questionCount > 0) {
          // Create group score name based on group name
          const groupScoreName = `totalScore_${group.name.replace('-', '_').toUpperCase()}`;
          scores[groupScoreName] = totalScore / questionCount;
        }
      }
    });

    // For backward compatibility with mock data, also calculate some standard group names
    if (scores.question1Score !== undefined && scores.question2Score !== undefined) {
      scores.totalScore_DL = (scores.question1Score + scores.question2Score) / 2;
    }
    if (scores.question3Score !== undefined && scores.question4Score !== undefined) {
      scores.totalScore_CN = (scores.question3Score + scores.question4Score) / 2;
    }
  }

  // Evaluate completion condition based on scores
  static evaluateCompletionCondition(scores: Record<string, number>, conditions: any[]): string {
    for (const condition of conditions) {
      if (this.evaluateExpression(condition.expression, scores)) {
        return condition.html;
      }
    }

    // Return default completion message if no condition matches
    return "Cảm ơn bạn đã hoàn thành khảo sát!";
  }

  // Simple expression evaluator for completion conditions
  private static evaluateExpression(expression: string, scores: Record<string, number>): boolean {
    try {
      // Replace score variables with actual values
      let evaluatedExpression = expression;

      Object.entries(scores).forEach(([key, value]) => {
        const regex = new RegExp(`\\{${key}\\}`, 'g');
        evaluatedExpression = evaluatedExpression.replace(regex, value.toString());
      });

      // Simple evaluation for basic conditions
      // This is a simplified version - in production, use a proper expression parser
      evaluatedExpression = evaluatedExpression
        .replace(/and/g, '&&')
        .replace(/or/g, '||');

      return eval(evaluatedExpression);
    } catch (error) {
      console.error('Error evaluating expression:', expression, error);
      return false;
    }
  }
}
