import { shuffle } from 'lodash';
import { Survey, SurveyJSModel, SurveyJSPage, SurveyJSQuestion, SurveyJSChoice, CalculatedValue } from '../types/survey';

export class SurveyService {
  // Convert Strapi survey data to SurveyJS format
  static convertToSurveyJS(survey: Survey): SurveyJSModel {
    const pages: SurveyJSPage[] = survey.questionGroups.map(group => ({
      name: group.name,
      title: group.title,
      elements: this.convertQuestionsToSurveyJS(group)
    }));

    return {
      locale: survey.locale,
      title: survey.title,
      description: survey.description,
      completedHtml: survey.completedHtml,
      completedHtmlOnCondition: survey.completedHtmlOnCondition,
      pages,
      calculatedValues: survey.calculatedValues
    };
  }

  // Convert questions from all subgroups to SurveyJS format
  private static convertQuestionsToSurveyJS(group: any): SurveyJSQuestion[] {
    const questions: SurveyJSQuestion[] = [];
    
    group.subGroups.forEach((subGroup: any) => {
      subGroup.questions.forEach((question: any) => {
        const surveyJSQuestion: SurveyJSQuestion = {
          type: question.type,
          name: question.name,
          title: question.title,
          description: question.description ? `${subGroup.name} - ${question.description}` : subGroup.name,
          isRequired: question.isRequired,
          choices: this.shuffleChoices(question.choices)
        };
        
        questions.push(surveyJSQuestion);
      });
    });
    
    return questions;
  }

  // Shuffle choices while preserving score mapping
  private static shuffleChoices(choices: any[]): SurveyJSChoice[] {
    const shuffledChoices = shuffle([...choices]);
    
    return shuffledChoices.map(choice => ({
      value: choice.value,
      text: choice.text
    }));
  }

  // Calculate scores from survey results
  static calculateScores(surveyData: Record<string, any>, originalSurvey: Survey): Record<string, number> {
    const scores: Record<string, number> = {};
    
    // Create a mapping of question name to choices for score lookup
    const questionChoicesMap = new Map<string, Map<string, number>>();
    
    originalSurvey.questionGroups.forEach(group => {
      group.subGroups.forEach(subGroup => {
        subGroup.questions.forEach(question => {
          const choicesMap = new Map<string, number>();
          question.choices.forEach(choice => {
            choicesMap.set(choice.value, choice.score);
          });
          questionChoicesMap.set(question.name, choicesMap);
        });
      });
    });

    // Calculate individual question scores
    Object.entries(surveyData).forEach(([questionName, answer]) => {
      if (questionChoicesMap.has(questionName) && answer) {
        const choicesMap = questionChoicesMap.get(questionName)!;
        const score = choicesMap.get(answer as string) || 0;
        scores[`${questionName}Score`] = score;
      }
    });

    // Calculate group averages based on the original survey structure
    this.calculateGroupScores(scores, originalSurvey);

    return scores;
  }

  // Calculate group average scores
  private static calculateGroupScores(scores: Record<string, number>, survey: Survey) {
    // Define question ranges for each group based on survey-data.json structure
    const groupRanges = {
      'totalScore_DL': { start: 1, end: 11 }, // Data questions 1-11
      'totalScore_CN': { start: 12, end: 26 }, // Technology questions 12-26
      'totalScore_CNVHTO': { start: 27, end: 42 }, // People, Culture, Organization questions 27-42
      'totalScore_QTVVH': { start: 43, end: 62 }, // Management and Operations questions 43-62
      'totalScore_CLVLD': { start: 63, end: 82 } // Strategy and Leadership questions 63-82
    };

    Object.entries(groupRanges).forEach(([groupName, range]) => {
      let totalScore = 0;
      let questionCount = 0;

      for (let i = range.start; i <= range.end; i++) {
        const questionScore = scores[`question${i}Score`];
        if (questionScore !== undefined) {
          totalScore += questionScore;
          questionCount++;
        }
      }

      if (questionCount > 0) {
        scores[groupName] = totalScore / questionCount;
      }
    });
  }

  // Evaluate completion condition based on scores
  static evaluateCompletionCondition(scores: Record<string, number>, conditions: any[]): string {
    for (const condition of conditions) {
      if (this.evaluateExpression(condition.expression, scores)) {
        return condition.html;
      }
    }
    
    // Return default completion message if no condition matches
    return "Cảm ơn bạn đã hoàn thành khảo sát!";
  }

  // Simple expression evaluator for completion conditions
  private static evaluateExpression(expression: string, scores: Record<string, number>): boolean {
    try {
      // Replace score variables with actual values
      let evaluatedExpression = expression;
      
      Object.entries(scores).forEach(([key, value]) => {
        const regex = new RegExp(`\\{${key}\\}`, 'g');
        evaluatedExpression = evaluatedExpression.replace(regex, value.toString());
      });

      // Simple evaluation for basic conditions
      // This is a simplified version - in production, use a proper expression parser
      evaluatedExpression = evaluatedExpression
        .replace(/and/g, '&&')
        .replace(/or/g, '||');

      return eval(evaluatedExpression);
    } catch (error) {
      console.error('Error evaluating expression:', expression, error);
      return false;
    }
  }
}
