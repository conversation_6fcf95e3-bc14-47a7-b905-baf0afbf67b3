import axios from 'axios';
import { Survey, UserInfo, SurveyResult } from '../types/survey';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:1337/api';
const API_TOKEN = import.meta.env.VITE_API_TOKEN || '';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { 'Authorization': `Bearer ${API_TOKEN}` })
  },
  timeout: 10000,
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.error('Unauthorized access - check API token');
    } else if (error.response?.status === 403) {
      console.error('Forbidden access - insufficient permissions');
    } else if (error.response?.status >= 500) {
      console.error('Server error - please try again later');
    }
    
    return Promise.reject(error);
  }
);

export const surveyApi = {
  // Get survey structure with all related data
  async getSurvey(): Promise<Survey> {
    try {
      const response = await api.get('/surveys?populate[questionGroups][populate][subGroups][populate][questions][populate]=choices');
      
      if (!response.data?.data?.[0]) {
        throw new Error('No survey data found');
      }
      
      return response.data.data[0];
    } catch (error) {
      console.error('Error fetching survey:', error);
      throw new Error('Failed to load survey data');
    }
  },

  // Create user info
  async createUserInfo(userInfo: Omit<UserInfo, 'id'>): Promise<{ id: number }> {
    try {
      const response = await api.post('/user-infos', {
        data: userInfo
      });
      
      return response.data.data;
    } catch (error) {
      console.error('Error creating user info:', error);
      throw new Error('Failed to save user information');
    }
  },

  // Submit survey result
  async submitSurveyResult(surveyResult: Omit<SurveyResult, 'id' | 'completedAt'>): Promise<{ id: number }> {
    try {
      const response = await api.post('/survey-results', {
        data: {
          ...surveyResult,
          completedAt: new Date().toISOString()
        }
      });
      
      return response.data.data;
    } catch (error) {
      console.error('Error submitting survey result:', error);
      throw new Error('Failed to submit survey results');
    }
  }
};

export default api;
