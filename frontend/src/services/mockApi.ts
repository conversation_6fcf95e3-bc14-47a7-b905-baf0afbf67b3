import { Survey, UserInfo, SurveyResult } from '../types/survey';

// Mock survey data for testing
const mockSurvey: Survey = {
  id: 1,
  title: 'Khảo sát AI',
  description: 'Khảo sát đánh giá mức độ sẵn sàng AI của doanh nghiệp',
  locale: 'vi',
  completedHtml: 'Cảm ơn bạn đã hoàn thành khảo sát!',
  completedHtmlOnCondition: [
    {
      expression: '{totalScore_DL} > 3 and {totalScore_CN} > 3',
      html: '<h2>Xuất sắc!</h2><p><PERSON><PERSON>h nghiệp của bạn đã sẵn sàng tích hợp trí tuệ nhân tạo một cách toàn diện.</p>'
    },
    {
      expression: '{totalScore_DL} > 2 or {totalScore_CN} > 2',
      html: '<h2>Tốt!</h2><p><PERSON><PERSON><PERSON> nghiệ<PERSON> của bạn đang trên đường phát triển AI.</p>'
    }
  ],
  calculatedValues: [
    { name: 'totalScore_DL', expression: '({question1Score} + {question2Score}) / 2' },
    { name: 'totalScore_CN', expression: '({question3Score} + {question4Score}) / 2' }
  ],
  questionGroups: [
    {
      id: 1,
      name: 'data-group',
      title: 'Dữ liệu',
      survey: 1,
      subGroups: [
        {
          id: 1,
          name: 'Quản trị dữ liệu',
          questionGroup: 1,
          questions: [
            {
              id: 1,
              name: 'question1',
              title: 'Doanh nghiệp đã có khung quản trị dữ liệu chính thức chưa?',
              description: 'Khung quản trị, chính sách, tiêu chuẩn dữ liệu',
              type: 'radiogroup',
              isRequired: true,
              subGroup: 1,
              choices: [
                { id: 1, value: 'a', text: 'Chưa có', score: 1, question: 1 },
                { id: 2, value: 'b', text: 'Đang xây dựng', score: 2, question: 1 },
                { id: 3, value: 'c', text: 'Có nhưng chưa triển khai đầy đủ', score: 3, question: 1 },
                { id: 4, value: 'd', text: 'Có và đã triển khai hiệu quả', score: 4, question: 1 }
              ]
            },
            {
              id: 2,
              name: 'question2',
              title: 'Doanh nghiệp có quy trình kiểm soát chất lượng dữ liệu không?',
              description: 'Quy trình kiểm soát chất lượng dữ liệu',
              type: 'radiogroup',
              isRequired: true,
              subGroup: 1,
              choices: [
                { id: 5, value: 'a', text: 'Không có', score: 1, question: 2 },
                { id: 6, value: 'b', text: 'Có nhưng không thường xuyên', score: 2, question: 2 },
                { id: 7, value: 'c', text: 'Có và thực hiện định kỳ', score: 3, question: 2 },
                { id: 8, value: 'd', text: 'Có hệ thống tự động kiểm soát', score: 4, question: 2 }
              ]
            }
          ]
        }
      ]
    },
    {
      id: 2,
      name: 'tech-group',
      title: 'Công nghệ',
      survey: 1,
      subGroups: [
        {
          id: 2,
          name: 'Hạ tầng công nghệ',
          questionGroup: 2,
          questions: [
            {
              id: 3,
              name: 'question3',
              title: 'Hạ tầng công nghệ hiện tại có hỗ trợ AI không?',
              description: 'Khả năng xử lý, lưu trữ dữ liệu lớn',
              type: 'radiogroup',
              isRequired: true,
              subGroup: 2,
              choices: [
                { id: 9, value: 'a', text: 'Không hỗ trợ', score: 1, question: 3 },
                { id: 10, value: 'b', text: 'Hỗ trợ một phần', score: 2, question: 3 },
                { id: 11, value: 'c', text: 'Hỗ trợ tốt', score: 3, question: 3 },
                { id: 12, value: 'd', text: 'Hỗ trợ toàn diện', score: 4, question: 3 }
              ]
            },
            {
              id: 4,
              name: 'question4',
              title: 'Đội ngũ IT có kinh nghiệm với AI/ML không?',
              description: 'Kinh nghiệm triển khai và vận hành AI',
              type: 'radiogroup',
              isRequired: true,
              subGroup: 2,
              choices: [
                { id: 13, value: 'a', text: 'Không có kinh nghiệm', score: 1, question: 4 },
                { id: 14, value: 'b', text: 'Có kinh nghiệm cơ bản', score: 2, question: 4 },
                { id: 15, value: 'c', text: 'Có kinh nghiệm tốt', score: 3, question: 4 },
                { id: 16, value: 'd', text: 'Có kinh nghiệm chuyên sâu', score: 4, question: 4 }
              ]
            }
          ]
        }
      ]
    }
  ]
};

export const mockSurveyApi = {
  async getSurvey(): Promise<Survey> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockSurvey;
  },

  async createUserInfo(userInfo: Omit<UserInfo, 'id'>): Promise<{ id: number }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log('Mock: Created user info:', userInfo);
    return { id: Date.now() }; // Use timestamp as mock ID
  },

  async submitSurveyResult(surveyResult: Omit<SurveyResult, 'id' | 'completedAt'>): Promise<{ id: number }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Mock: Submitted survey result:', surveyResult);
    return { id: Date.now() }; // Use timestamp as mock ID
  }
};
