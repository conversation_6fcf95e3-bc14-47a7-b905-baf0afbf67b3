import React, { useEffect, useState } from 'react';
import { Model } from 'survey-core';
import { Survey } from 'survey-react-ui';
import { SurveyJSModel } from '../types/survey';
import { SurveyService } from '../services/surveyService';

interface SurveyFormProps {
  surveyModel: SurveyJSModel;
  onComplete: (result: any, scores: Record<string, number>) => void;
  onError?: (error: string) => void;
}

export const SurveyForm: React.FC<SurveyFormProps> = ({ 
  surveyModel, 
  onComplete, 
  onError 
}) => {
  const [survey, setSurvey] = useState<Model | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      // Create SurveyJS model
      const model = new Model(surveyModel);
      
      // Configure survey settings
      model.showProgressBar = 'top';
      model.progressBarType = 'pages';
      model.showQuestionNumbers = 'on';
      model.questionTitleLocation = 'top';
      model.showCompletedPage = false;
      
      // Handle survey completion
      model.onComplete.add((sender) => {
        try {
          const surveyData = sender.data;
          
          // Calculate scores using the original survey structure
          // Note: We need the original survey structure for score calculation
          // This would typically be passed from the parent component
          const scores = SurveyService.calculateScores(surveyData, surveyModel as any);
          
          onComplete(surveyData, scores);
        } catch (error) {
          console.error('Error processing survey completion:', error);
          onError?.('Có lỗi xảy ra khi xử lý kết quả khảo sát');
        }
      });

      // Handle survey errors
      model.onError.add((sender, options) => {
        console.error('Survey error:', options.error);
        onError?.('Có lỗi xảy ra trong quá trình khảo sát');
      });

      setSurvey(model);
      setIsLoading(false);
    } catch (error) {
      console.error('Error creating survey model:', error);
      onError?.('Không thể tải khảo sát');
      setIsLoading(false);
    }
  }, [surveyModel, onComplete, onError]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải khảo sát...</p>
        </div>
      </div>
    );
  }

  if (!survey) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Không thể tải khảo sát</h2>
          <p className="text-gray-600">Vui lòng thử lại sau hoặc liên hệ hỗ trợ.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <Survey model={survey} />
        </div>
      </div>
    </div>
  );
};
