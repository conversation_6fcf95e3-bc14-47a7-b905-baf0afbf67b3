import React from 'react';

interface SurveyResultsProps {
  completionHtml: string;
  scores: Record<string, number>;
  onRestart?: () => void;
}

export const SurveyResults: React.FC<SurveyResultsProps> = ({ 
  completionHtml, 
  scores, 
  onRestart 
}) => {
  // Format scores for display
  const formatScore = (score: number): string => {
    return score.toFixed(2);
  };

  const groupScores = {
    'Chiến lược và Lãnh đạo': scores.totalScore_CLVLD,
    'Dữ liệu': scores.totalScore_DL,
    'Công nghệ': scores.totalScore_CN,
    '<PERSON>, <PERSON><PERSON><PERSON> hó<PERSON>, <PERSON>ổ chức': scores.totalScore_CNVHTO,
    'Quản trị và Vận hành': scores.totalScore_QTVVH
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="text-green-500 text-6xl mb-4">✅</div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Hoàn thành khảo sát!
            </h1>
            <p className="text-gray-600">
              Cảm ơn bạn đã dành thời gian tham gia khảo sát của chúng tôi.
            </p>
          </div>

          {/* Scores Summary */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Kết quả điểm số
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(groupScores).map(([groupName, score]) => (
                <div key={groupName} className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-700 mb-2">{groupName}</h3>
                  <div className="text-2xl font-bold text-primary-600">
                    {score ? formatScore(score) : 'N/A'}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-primary-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${score ? (score / 4) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Detailed Results */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Đánh giá chi tiết
            </h2>
            <div 
              className="prose max-w-none text-gray-700"
              dangerouslySetInnerHTML={{ __html: completionHtml }}
            />
          </div>

          {/* Actions */}
          <div className="text-center space-y-4">
            {onRestart && (
              <button
                onClick={onRestart}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Làm lại khảo sát
              </button>
            )}
            
            <div className="text-sm text-gray-500">
              <p>Kết quả này đã được lưu trữ an toàn.</p>
              <p>Nếu bạn có thắc mắc, vui lòng liên hệ với chúng tôi.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
