import React, { useState } from 'react';
import { UserInfoFormData, validateUserInfo, getValidationErrors } from '../utils/validation';

interface UserInfoFormProps {
  onSubmit: (userInfo: UserInfoFormData) => void;
  isLoading?: boolean;
}

export const UserInfoForm: React.FC<UserInfoFormProps> = ({ onSubmit, isLoading = false }) => {
  const [formData, setFormData] = useState<UserInfoFormData>({
    name: '',
    email: ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const handleInputChange = (field: keyof UserInfoFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBlur = (field: keyof UserInfoFormData) => () => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate single field on blur
    const result = validateUserInfo({ [field]: formData[field] });
    if (!result.success) {
      const fieldErrors = getValidationErrors(result.error);
      setErrors(prev => ({ ...prev, ...fieldErrors }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    setTouched({ name: true, email: true });
    
    // Validate all fields
    const result = validateUserInfo(formData);
    
    if (!result.success) {
      const validationErrors = getValidationErrors(result.error);
      setErrors(validationErrors);
      return;
    }
    
    // Clear errors and submit
    setErrors({});
    onSubmit(result.data);
  };

  const isFieldError = (field: keyof UserInfoFormData) => {
    return touched[field] && !!errors[field];
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Thông tin người tham gia
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Vui lòng nhập thông tin của bạn để bắt đầu khảo sát
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="form-group">
              <label htmlFor="name" className="form-label">
                Họ và tên *
              </label>
              <input
                id="name"
                name="name"
                type="text"
                autoComplete="name"
                required
                className={`form-input ${isFieldError('name') ? 'error' : ''}`}
                placeholder="Nhập họ và tên của bạn"
                value={formData.name}
                onChange={handleInputChange('name')}
                onBlur={handleBlur('name')}
                disabled={isLoading}
              />
              {isFieldError('name') && (
                <p className="form-error">{errors.name}</p>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="email" className="form-label">
                Email *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className={`form-input ${isFieldError('email') ? 'error' : ''}`}
                placeholder="Nhập địa chỉ email của bạn"
                value={formData.email}
                onChange={handleInputChange('email')}
                onBlur={handleBlur('email')}
                disabled={isLoading}
              />
              {isFieldError('email') && (
                <p className="form-error">{errors.email}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="form-button"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang xử lý...
                </span>
              ) : (
                'Bắt đầu khảo sát'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
