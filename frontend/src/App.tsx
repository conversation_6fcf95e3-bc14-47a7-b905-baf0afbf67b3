import React, { useState, useEffect } from 'react';
import { UserInfoForm } from './components/UserInfoForm';
import { SurveyForm } from './components/SurveyForm';
import { SurveyResults } from './components/SurveyResults';
import { ErrorBoundary } from './components/ErrorBoundary';
import { surveyApi } from './services/api';
import { SurveyService } from './services/surveyService';
import { UserInfoFormData } from './utils/validation';
import { Survey, SurveyJSModel } from './types/survey';

type AppState = 'userInfo' | 'survey' | 'results' | 'error';

interface AppData {
  userInfo?: UserInfoFormData;
  userInfoId?: number;
  surveyModel?: SurveyJSModel;
  originalSurvey?: Survey;
  surveyResults?: any;
  scores?: Record<string, number>;
  completionHtml?: string;
  error?: string;
}

function App() {
  const [state, setState] = useState<AppState>('userInfo');
  const [data, setData] = useState<AppData>({});
  const [isLoading, setIsLoading] = useState(false);

  // Load survey data on app initialization
  useEffect(() => {
    const loadSurvey = async () => {
      try {
        setIsLoading(true);
        const survey = await surveyApi.getSurvey();
        const surveyModel = SurveyService.convertToSurveyJS(survey);
        
        setData(prev => ({ 
          ...prev, 
          surveyModel, 
          originalSurvey: survey 
        }));
      } catch (error) {
        console.error('Failed to load survey:', error);
        setData(prev => ({ 
          ...prev, 
          error: 'Không thể tải dữ liệu khảo sát. Vui lòng thử lại sau.' 
        }));
        setState('error');
      } finally {
        setIsLoading(false);
      }
    };

    loadSurvey();
  }, []);

  const handleUserInfoSubmit = async (userInfo: UserInfoFormData) => {
    try {
      setIsLoading(true);
      const userInfoResponse = await surveyApi.createUserInfo(userInfo);
      
      setData(prev => ({ 
        ...prev, 
        userInfo, 
        userInfoId: userInfoResponse.id 
      }));
      setState('survey');
    } catch (error) {
      console.error('Failed to save user info:', error);
      setData(prev => ({ 
        ...prev, 
        error: 'Không thể lưu thông tin người dùng. Vui lòng thử lại.' 
      }));
      setState('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSurveyComplete = async (surveyResults: any, scores: Record<string, number>) => {
    try {
      setIsLoading(true);
      
      if (!data.userInfoId || !data.originalSurvey) {
        throw new Error('Missing user info or survey data');
      }

      // Submit survey results
      await surveyApi.submitSurveyResult({
        result: surveyResults,
        scores,
        userInfo: data.userInfoId
      });

      // Evaluate completion condition
      const completionHtml = SurveyService.evaluateCompletionCondition(
        scores, 
        data.originalSurvey.completedHtmlOnCondition
      );

      setData(prev => ({ 
        ...prev, 
        surveyResults, 
        scores, 
        completionHtml 
      }));
      setState('results');
    } catch (error) {
      console.error('Failed to submit survey results:', error);
      setData(prev => ({ 
        ...prev, 
        error: 'Không thể lưu kết quả khảo sát. Vui lòng thử lại.' 
      }));
      setState('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSurveyError = (error: string) => {
    setData(prev => ({ ...prev, error }));
    setState('error');
  };

  const handleRestart = () => {
    setData({});
    setState('userInfo');
  };

  const handleRetry = () => {
    if (state === 'error') {
      // Determine which state to return to based on available data
      if (data.scores && data.completionHtml) {
        setState('results');
      } else if (data.surveyModel && data.userInfoId) {
        setState('survey');
      } else {
        setState('userInfo');
      }
    }
  };

  // Loading state
  if (isLoading && state === 'userInfo' && !data.surveyModel) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang khởi tạo ứng dụng...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (state === 'error') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <div>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-3xl font-extrabold text-gray-900 mb-2">
              Có lỗi xảy ra
            </h2>
            <p className="text-gray-600 mb-6">
              {data.error || 'Đã xảy ra lỗi không mong muốn.'}
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={handleRetry}
              className="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"
            >
              Thử lại
            </button>
            
            <button
              onClick={handleRestart}
              className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
            >
              Bắt đầu lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="App">
        {state === 'userInfo' && (
          <UserInfoForm 
            onSubmit={handleUserInfoSubmit} 
            isLoading={isLoading}
          />
        )}
        
        {state === 'survey' && data.surveyModel && (
          <SurveyForm 
            surveyModel={data.surveyModel}
            onComplete={handleSurveyComplete}
            onError={handleSurveyError}
          />
        )}
        
        {state === 'results' && data.completionHtml && data.scores && (
          <SurveyResults 
            completionHtml={data.completionHtml}
            scores={data.scores}
            onRestart={handleRestart}
          />
        )}
      </div>
    </ErrorBoundary>
  );
}

export default App;
