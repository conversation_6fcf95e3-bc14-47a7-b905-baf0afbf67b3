export interface Choice {
  id: number;
  value: string;
  text: string;
  score: number;
  question: number;
}

export interface Question {
  id: number;
  name: string;
  title: string;
  description?: string;
  type: string;
  isRequired: boolean;
  choices: Choice[];
  subGroup: number;
}

export interface SubGroup {
  id: number;
  name: string;
  questions: Question[];
  questionGroup: number;
}

export interface QuestionGroup {
  id: number;
  name: string;
  title: string;
  subGroups: SubGroup[];
  survey: number;
}

export interface Survey {
  id: number;
  title: string;
  description: string;
  locale: string;
  completedHtml: string;
  completedHtmlOnCondition: CompletedHtmlCondition[];
  calculatedValues: CalculatedValue[];
  questionGroups: QuestionGroup[];
}

export interface CompletedHtmlCondition {
  expression: string;
  html: string;
}

export interface CalculatedValue {
  name: string;
  expression: string;
}

export interface UserInfo {
  name: string;
  email: string;
}

export interface SurveyResult {
  result: Record<string, any>;
  scores: Record<string, number>;
  completedAt: string;
  userInfo: number;
}

// SurveyJS format interfaces
export interface SurveyJSChoice {
  value: string;
  text: string;
}

export interface SurveyJSQuestion {
  type: string;
  name: string;
  title: string;
  description?: string;
  isRequired: boolean;
  choices: SurveyJSChoice[];
}

export interface SurveyJSPage {
  name: string;
  title: string;
  elements: SurveyJSQuestion[];
}

export interface SurveyJSModel {
  locale: string;
  title: string;
  description: string;
  completedHtml: string;
  completedHtmlOnCondition: CompletedHtmlCondition[];
  pages: SurveyJSPage[];
  calculatedValues: CalculatedValue[];
}
