# AI Survey Frontend

Frontend application for the AI Survey project built with React, Vite, SurveyJS, and TypeScript.

## Features

- 📋 Interactive survey interface using SurveyJS
- 🎨 Modern UI with Tailwind CSS
- 🔀 Randomized answer choices while preserving scoring
- ✅ Form validation with Zod
- 🔒 Security headers and input sanitization
- 📱 Responsive design
- 🧪 Comprehensive E2E testing with Cypress

## Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Survey Library**: SurveyJS
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **Validation**: Zod
- **Testing**: Cypress (E2E)
- **Security**: Helmet for CSP headers

## Prerequisites

- Node.js 18+ 
- npm or yarn
- Backend API running on http://localhost:1337

## Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Update `.env` with your API configuration:
```env
VITE_API_URL=http://localhost:1337/api
VITE_API_TOKEN=your_strapi_api_token
```

## Development

Start the development server:
```bash
npm run dev
```

The application will be available at http://localhost:3000

## Building

Build for production:
```bash
npm run build
```

Preview production build:
```bash
npm run preview
```

## Testing

### E2E Tests with Cypress

Run tests in headless mode:
```bash
npm run cypress:run
```

Open Cypress Test Runner:
```bash
npm run cypress:open
```

### Test Coverage

The E2E tests cover:
- ✅ User information form validation
- ✅ Survey flow and navigation
- ✅ Answer randomization and scoring
- ✅ Results display and calculation
- ✅ Error handling and edge cases
- ✅ Security validation
- ✅ API integration

## Project Structure

```
src/
├── components/          # React components
│   ├── UserInfoForm.tsx    # User information form
│   ├── SurveyForm.tsx      # Main survey component
│   ├── SurveyResults.tsx   # Results display
│   └── ErrorBoundary.tsx   # Error handling
├── services/            # API and business logic
│   ├── api.ts              # Axios API client
│   └── surveyService.ts    # Survey data processing
├── types/               # TypeScript type definitions
│   └── survey.ts           # Survey-related types
├── utils/               # Utility functions
│   └── validation.ts       # Zod validation schemas
└── App.tsx             # Main application component
```

## Key Features

### Survey Flow
1. **User Information**: Collect name and email with validation
2. **Survey Display**: Show questions with randomized choices
3. **Score Calculation**: Calculate scores based on original mapping
4. **Results**: Display completion message and scores

### Answer Randomization
- Choices are shuffled for each question display
- Original value-to-score mapping is preserved (a=1, b=2, c=3, d=4)
- Scoring remains accurate regardless of display order

### Security
- Input validation and sanitization
- CSP headers for XSS protection
- Email format validation
- Error boundary for graceful error handling

### Responsive Design
- Mobile-first approach with Tailwind CSS
- Accessible form controls
- Loading states and error messages
- Progress indicators

## API Integration

The frontend communicates with the Strapi backend through:

- `GET /api/surveys` - Fetch survey structure
- `POST /api/user-infos` - Save user information
- `POST /api/survey-results` - Submit survey results

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_URL` | Backend API URL | `http://localhost:1337/api` |
| `VITE_API_TOKEN` | Strapi API token | `` |
| `VITE_NODE_ENV` | Environment mode | `development` |

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure backend is running on port 1337
   - Check CORS configuration in Strapi
   - Verify API token is correct

2. **Survey Not Loading**
   - Check browser console for errors
   - Verify survey data exists in Strapi
   - Ensure proper API permissions

3. **Cypress Tests Failing**
   - Ensure both frontend and backend are running
   - Check test data is properly seeded
   - Verify API endpoints are accessible

### Debug Mode

Enable debug logging by setting:
```env
VITE_NODE_ENV=development
```

## Contributing

1. Follow TypeScript best practices
2. Add tests for new features
3. Update documentation
4. Ensure accessibility compliance
5. Test on multiple browsers

## License

MIT License - see LICENSE file for details
