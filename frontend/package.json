{"name": "aisurvey-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "survey-core": "^1.9.131", "survey-react-ui": "^1.9.131", "axios": "^1.6.2", "zod": "^3.22.4", "lodash": "^4.17.21", "helmet": "^7.1.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "cypress": "^13.6.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}}