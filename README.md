### Đề bài: <PERSON><PERSON><PERSON> dựng ứng dụng khảo sát sử dụng SurveyJS với backend <PERSON><PERSON><PERSON>, quản lý câu hỏi và điểm số chi tiết

#### Mô tả dự án
Bạn được yêu cầu xây dựng một ứng dụng web sử dụng **SurveyJS** (thư viện khảo sát mã mở) để hiển thị và thu thập dữ liệu khảo sát từ người dùng, dựa trên dữ liệu từ file `survey-data.json`. Ứng dụng sử dụng **Strapi** làm backend để lưu trữ thông tin người dùng (tên, email), kết quả khảo sát, và cấu trúc khảo sát (bao gồm câu hỏi, nhóm câu hỏi, nhóm phụ, và điểm số). Dữ liệu khảo sát được nhập từ `survey-data.json` vào <PERSON>. <PERSON><PERSON><PERSON> đáp án của mỗi câu hỏi được sắp xếp ngẫu nhiên khi hiển thị, nhưng điểm số phải được bảo toàn theo ID và mã của đáp án trong JSON. Cơ chế tính điểm được giữ nguyên như trong `survey-data.json`. Ứng dụng được viết bằng **TypeScript**, tổ chức theo mô hình mã mở, tích hợp bảo mật, và bao gồm **E2E test** sử dụng **Cypress**.

#### Yêu cầu chức năng
1. **Nhập và quản lý dữ liệu khảo sát từ `survey-data.json`**:
    - Nhập cấu trúc khảo sát từ file `survey-data.json` vào Strapi để tạo API endpoint cung cấp dữ liệu khảo sát.
    - Quản lý dữ liệu khảo sát trong Strapi với các **collection types**:
        - **Survey**: Lưu cấu trúc khảo sát tổng thể (title, description, pages, calculatedValues, completedHtmlOnCondition).
        - **QuestionGroup**: Lưu các nhóm câu hỏi (tương ứng với `pages` trong JSON, ví dụ: "Dữ liệu", "Công nghệ", "Con người, Văn hóa, Tổ chức").
        - **SubGroup**: Lưu các nhóm phụ trong mỗi nhóm câu hỏi (tương ứng với `description` trong JSON, ví dụ: "Khung quản trị, chính sách, tiêu chuẩn dữ liệu", "Quản trị vòng đời dữ liệu").
        - **Question**: Lưu thông tin từng câu hỏi (title, description, type, choices, isRequired).
        - **Choice**: Lưu các đáp án của mỗi câu hỏi (value, text, score theo ID/mã).
    - Mỗi **Choice** phải có trường `score` (1, 2, 3, 4 tương ứng với value `a`, `b`, `c`, `d`) được lưu trữ trong Strapi, dựa trên `calculatedValues` trong `survey-data.json`.
    - API Strapi hỗ trợ:
        - GET để lấy cấu trúc khảo sát (bao gồm nhóm câu hỏi, nhóm phụ, câu hỏi, và đáp án).
        - POST để lưu thông tin người dùng và kết quả khảo sát.

2. **Hiển thị khảo sát**:
    - Tải dữ liệu khảo sát từ API Strapi (hoặc file `survey-data.json` nếu cần trong giai đoạn phát triển).
    - Sử dụng SurveyJS để hiển thị khảo sát trên giao diện web, tổ chức theo **nhóm câu hỏi** (pages) và hiển thị **nhóm phụ** (subgroups) rõ ràng (ví dụ: tiêu đề phụ trong giao diện).
    - **Sắp xếp ngẫu nhiên đáp án**:
        - Mỗi câu hỏi (`radiogroup`) hiển thị các đáp án (`choices`) theo thứ tự ngẫu nhiên khi tải khảo sát.
        - Đảm bảo điểm số của đáp án được bảo toàn theo ID/mã (value `a`=1, `b`=2, `c`=3, `d`=4) bất kể thứ tự hiển thị.
        - Ví dụ: Nếu đáp án `a` (score=1) được hiển thị ở vị trí thứ 3, khi người dùng chọn, hệ thống vẫn ghi nhận điểm là 1.
    - Giao diện responsive, sử dụng **Tailwind CSS** để tối ưu hóa thiết kế, với các nhóm câu hỏi và nhóm phụ được định dạng rõ ràng (ví dụ: sử dụng heading, divider).

3. **Thu thập thông tin người dùng**:
    - Trước khi bắt đầu khảo sát, hiển thị form yêu cầu người dùng nhập thông tin (tên, email).
    - Validate email bằng **Zod** để đảm bảo định dạng hợp lệ trước khi gửi.
    - Lưu thông tin người dùng và kết quả khảo sát vào Strapi.

4. **Lưu trữ dữ liệu với Strapi**:
    - Tạo các **collection types** trong Strapi:
        - **UserInfo**: Lưu thông tin người dùng (tên, email, mã hóa email bằng bcrypt).
        - **SurveyResult**: Lưu kết quả khảo sát (dữ liệu JSON từ SurveyJS, bao gồm đáp án đã chọn và điểm số), liên kết với UserInfo, và thời gian hoàn thành (timestamp).
        - **Survey**, **QuestionGroup**, **SubGroup**, **Question**, **Choice**: Như mô tả ở mục 1.
    - API Strapi hỗ trợ:
        - POST `/user-info` để lưu thông tin người dùng.
        - POST `/survey-results` để lưu kết quả khảo sát.
        - GET `/surveys` để lấy cấu trúc khảo sát (bao gồm câu hỏi, nhóm, và đáp án).
    - Đảm bảo các endpoint yêu cầu xác thực bằng **API token** hoặc **JWT**.

5. **Cơ chế tính điểm**:
    - Giữ nguyên cơ chế tính điểm như trong `survey-data.json`:
        - Mỗi đáp án (`choice`) có điểm số cố định dựa trên `value`:
            - `a` = 1 điểm
            - `b` = 2 điểm
            - `c` = 3 điểm
            - `d` = 4 điểm
        - Tính điểm cho từng câu hỏi dựa trên `calculatedValues` (ví dụ: `question1Score` = `iif({question1} allof [a], 1, 0) + iif({question1} allof [b], 2, 0) + ...`).
        - Tính tổng điểm trung bình cho từng nhóm câu hỏi:
            - `totalScore_DL` (Dữ liệu): Trung bình điểm của các câu hỏi từ `question1` đến `question11`.
            - `totalScore_CN` (Công nghệ): Trung bình điểm của các câu hỏi từ `question12` đến `question26`.
            - `totalScore_CNVHTO` (Con người, Văn hóa, Tổ chức): Trung bình điểm của các câu hỏi từ `question27` đến `question42`.
            - `totalScore_QTVVH` (Quản trị và Vận hành): Trung bình điểm của các câu hỏi từ `question43` đến `question62`.
            - `totalScore_CLVLD` (Chiến lược và Lãnh đạo): Trung bình điểm của các câu hỏi từ `question63` đến `question82`.
        - Hiển thị kết quả đánh giá dựa trên `completedHtmlOnCondition`:
            - Ví dụ: Nếu `totalScore_CLVLD > 3.4` và `totalScore_CN > 3.6`..., hiển thị HTML tương ứng (xếp hạng cao nhất).
            - Nếu `totalScore_CLVLD < 2` hoặc bất kỳ nhóm nào dưới ngưỡng, hiển thị HTML cho xếp hạng thấp nhất.
    - Logic tính điểm có thể được triển khai:
        - Phía **frontend** (SurveyJS xử lý `calculatedValues` và `expression`).
        - Phía **backend** (Strapi xử lý điểm số khi nhận kết quả khảo sát, nếu cần tối ưu hiệu năng hoặc bảo mật).
    - Lưu điểm số của từng câu hỏi và tổng điểm trung bình vào `SurveyResult` trong Strapi.

6. **Mã mở**:
    - Tổ chức mã nguồn rõ ràng với cấu trúc thư mục hợp lý, tuân thủ nguyên tắc mã mở.
    - Cung cấp tài liệu (README) chi tiết, bao gồm:
        - Hướng dẫn nhập `survey-data.json` vào Strapi.
        - Hướng dẫn cài đặt và chạy frontend, backend Strapi, và test.
        - Quy trình đóng góp mã nguồn (CONTRIBUTING.md).
    - Đẩy mã nguồn lên GitHub với giấy phép mã mở (ví dụ: MIT).

7. **Bảo mật**:
    - **Frontend**:
        - Sử dụng **helmet** để thiết lập tiêu đề HTTP bảo mật (Content Security Policy, XSS protection).
        - Validate dữ liệu đầu vào (tên, email) bằng **Zod** trước khi gửi đến backend.
        - Sử dụng **HTTPS** cho tất cả các yêu cầu API.
    - **Backend (Strapi)**:
        - Sử dụng **API token** để xác thực yêu cầu từ frontend.
        - Cấu hình **CORS** để chỉ cho phép domain frontend.
        - Mã hóa email trong `UserInfo` bằng bcrypt (hoặc plugin Strapi phù hợp).
        - Yêu cầu xác thực **JWT** cho các endpoint nhạy cảm (POST/GET dữ liệu khảo sát).
        - Triển khai **rate limiting** để ngăn chặn tấn công brute force.
    - **Dữ liệu**:
        - Không lưu trữ email dưới dạng plain text.
        - Đảm bảo dữ liệu khảo sát được truyền qua HTTPS.
        - Audit log các thao tác nhạy cảm trong Strapi (ví dụ: lưu/gọi kết quả khảo sát).

8. **E2E Testing với Cypress**:
    - Viết các kịch bản E2E test để kiểm tra:
        - **Quản lý câu hỏi và nhóm**:
            - Tải và hiển thị khảo sát từ API Strapi, xác minh các nhóm câu hỏi (`pages`) và nhóm phụ (`description`) được hiển thị đúng.
            - Kiểm tra rằng các đáp án được sắp xếp ngẫu nhiên, nhưng điểm số được ghi nhận đúng theo ID/mã (a=1, b=2, c=3, d=4).
        - **Nhập thông tin người dùng**:
            - Kiểm tra form thông tin (tên, email) hiển thị trước khảo sát.
            - Kiểm tra lỗi khi nhập email không hợp lệ.
        - **Gửi khảo sát**:
            - Hoàn thành khảo sát, gửi dữ liệu, và xác minh dữ liệu được lưu vào Strapi (`UserInfo`, `SurveyResult`).
            - Kiểm tra điểm số từng câu hỏi và tổng điểm trung bình được lưu đúng.
        - **Kiểm tra tính điểm**:
            - Xác minh rằng điểm số được tính đúng theo `calculatedValues` và `completedHtmlOnCondition`.
            - Kiểm tra các trường hợp biên (ví dụ: tất cả chọn `a`, hoặc các tổ hợp điểm dẫn đến các xếp hạng khác nhau).
        - **Bảo mật**:
            - Kiểm tra rằng yêu cầu API không có token bị từ chối (mã 401/403).
            - Kiểm tra thông báo lỗi khi gửi khảo sát mà thiếu thông tin người dùng.
        - **Trường hợp lỗi**:
            - Kiểm tra lỗi khi API Strapi không phản hồi (timeout, server error).
            - Kiểm tra xử lý lỗi khi dữ liệu đầu vào không hợp lệ.
    - Đảm bảo test chạy ổn định và báo cáo kết quả rõ ràng (bao gồm screenshot hoặc video nếu cần).

#### Yêu cầu kỹ thuật
- **Ngôn ngữ lập trình**: TypeScript.
- **Frontend**:
    - Framework: **React** (khởi tạo bằng **Vite** để build nhanh).
    - Thư viện khảo sát: **SurveyJS** (phiên bản mã mở).
    - CSS: **Tailwind CSS** cho giao diện responsive.
    - Gọi API: **Axios** với TypeScript để tương tác với Strapi.
    - Validate dữ liệu: **Zod** cho form thông tin người dùng.
    - Bảo mật: **helmet** hoặc cấu hình CSP trong Vite.
    - Sắp xếp ngẫu nhiên đáp án: Sử dụng hàm random (ví dụ: `Math.random`) trong SurveyJS hoặc React để sắp xếp `choices`, nhưng ánh xạ điểm số theo `value` cố định.
- **Backend**: **Strapi v4.x** với **SQLite** để đơn giản hóa triển khai.
- **E2E Testing**: **Cypress**.
- **Công cụ quản lý mã nguồn**: Git (đẩy mã lên GitHub).
- **Cấu hình Strapi**:
    - Sử dụng SQLite làm cơ sở dữ liệu mặc định.
    - Cấu hình CORS để chỉ cho phép domain frontend.
    - Tạo **API token** cho frontend truy cập.
    - Kích hoạt **JWT** cho các endpoint nhạy cảm (nếu cần).
    - Tùy chỉnh Strapi để hỗ trợ lưu trữ cấu trúc khảo sát phức tạp (nhóm, nhóm phụ, câu hỏi, đáp án).
- **Nhập dữ liệu**:
    - Viết script (TypeScript hoặc JavaScript) hoặc sử dụng giao diện admin của Strapi để nhập dữ liệu từ `survey-data.json` vào các collection types (`Survey`, `QuestionGroup`, `SubGroup`, `Question`, `Choice`).
    - Đảm bảo ánh xạ đúng các trường như `pages`, `elements`, `choices`, `calculatedValues`, và `completedHtmlOnCondition`.

#### Công nghệ bổ sung để đơn giản hóa
- **Vite**: Tăng tốc phát triển và build so với `create-react-app`.
- **Tailwind CSS**: Giảm thời gian thiết kế giao diện, hỗ trợ responsive.
- **Axios**: Gọi API Strapi dễ dàng với TypeScript.
- **Zod**: Validate dữ liệu đầu vào phía client.
- **Strapi SQLite**: Không cần cài đặt cơ sở dữ liệu phức tạp.
- **Lodash**: Hỗ trợ xử lý mảng và đối tượng (ví dụ: sắp xếp ngẫu nhiên đáp án).

#### Kịch bản E2E Test chi tiết
1. **Test quản lý câu hỏi và nhóm**:
    - Kiểm tra rằng các nhóm câu hỏi (`pages`) như "Dữ liệu", "Công nghệ" được tải đúng từ Strapi.
    - Xác minh các nhóm phụ (`description`) được hiển thị rõ ràng trong giao diện.
    - Kiểm tra rằng các câu hỏi (`elements`) hiển thị đúng nội dung và loại (`radiogroup`).
    - Kiểm tra rằng các đáp án (`choices`) được sắp xếp ngẫu nhiên, nhưng điểm số được ghi nhận đúng theo `value` (a=1, b=2, c=3, d=4).

2. **Test nhập thông tin người dùng**:
    - Kiểm tra form thông tin (tên, email) hiển thị trước khảo sát.
    - Kiểm tra lỗi khi nhập email không hợp lệ (ví dụ: thiếu `@`, định dạng sai).
    - Xác minh rằng thông tin người dùng được lưu vào `UserInfo` trong Strapi.

3. **Test gửi khảo sát**:
    - Hoàn thành khảo sát với các lựa chọn ngẫu nhiên, gửi dữ liệu.
    - Xác minh rằng kết quả khảo sát được lưu vào `SurveyResult` với đúng điểm số và liên kết với `UserInfo`.
    - Kiểm tra API trả về mã trạng thái 200.

4. **Test tính điểm**:
    - Kiểm tra điểm số từng câu hỏi (dựa trên `calculatedValues`).
    - Xác minh tổng điểm trung bình (`totalScore_DL`, `totalScore_CN`, v.v.) được tính đúng.
    - Kiểm tra rằng kết quả đánh giá (`completedHtmlOnCondition`) hiển thị đúng theo ngưỡng điểm (ví dụ: xếp hạng cao nhất nếu `totalScore_CLVLD > 3.4`).
    - Test các trường hợp biên: tất cả chọn `a` (điểm thấp nhất), tất cả chọn `d` (điểm cao nhất).

5. **Test bảo mật**:
    - Kiểm tra rằng yêu cầu API không có token bị từ chối (mã 401/403).
    - Kiểm tra rằng yêu cầu gửi khảo sát thiếu thông tin người dùng bị từ chối.
    - Kiểm tra rằng email được mã hóa trong cơ sở dữ liệu.

6. **Test trường hợp lỗi**:
    - Kiểm tra lỗi khi API Strapi không phản hồi (timeout, server error).
    - Kiểm tra xử lý lỗi khi dữ liệu đầu vào không hợp lệ (ví dụ: email rỗng, câu hỏi bắt buộc bị bỏ qua).

#### Đầu ra mong đợi
- Ứng dụng web chạy được với giao diện khảo sát sử dụng **SurveyJS** và **Tailwind CSS**, hiển thị nhóm câu hỏi, nhóm phụ, và đáp án ngẫu nhiên.
- Backend **Strapi** lưu trữ thông tin người dùng, kết quả khảo sát, và cấu trúc khảo sát từ `survey-data.json`.
- Cơ chế tính điểm hoạt động đúng như trong `survey-data.json`, với điểm số được lưu trữ và kiểm tra chính xác.
- Đáp án hiển thị ngẫu nhiên nhưng điểm số được bảo toàn theo ID/mã.
- Bộ E2E test đầy đủ với **Cypress**, bao phủ các kịch bản quản lý câu hỏi, tính điểm, và bảo mật.
- Mã nguồn được đẩy lên GitHub với README chi tiết (hướng dẫn nhập dữ liệu, chạy ứng dụng, và test).

#### Ví dụ cấu trúc dự án
```
survey-app/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── SurveyForm.tsx
│   │   │   ├── UserInfoForm.tsx
│   │   │   ├── QuestionGroup.tsx
│   │   │   ├── SubGroup.tsx
│   │   ├── services/
│   │   │   ├── surveyService.ts
│   │   │   ├── api.ts
│   │   ├── types/
│   │   │   ├── survey.ts
│   │   │   ├── question.ts
│   │   │   ├── choice.ts
│   │   ├── App.tsx
│   │   ├── index.tsx
│   ├── cypress/
│   │   ├── e2e/
│   │   │   ├── survey.cy.ts
│   │   │   ├── scoring.cy.ts
│   │   │   ├── security.cy.ts
│   ├── public/
│   │   ├── survey-data.json
│   ├── README.md
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
├── backend/
│   ├── src/
│   │   ├── api/
│   │   │   ├── user-info/
│   │   │   ├── survey-result/
│   │   │   ├── survey/
│   │   │   ├── question-group/
│   │   │   ├── sub-group/
│   │   │   ├── question/
│   │   │   ├── choice/
│   ├── config/
│   ├── database.sqlite
│   ├── README.md
│   ├── package.json
└── README.md
```

#### Ví dụ minh họa (không bao gồm mã trực tiếp)
- **Quản lý câu hỏi và nhóm**:
    - Trong Strapi, mỗi `QuestionGroup` (ví dụ: "Dữ liệu") chứa nhiều `SubGroup` (ví dụ: "Khung quản trị, chính sách, tiêu chuẩn dữ liệu") và liên kết với nhiều `Question`.
    - Mỗi `Question` chứa danh sách `Choice` với các trường `value`, `text`, và `score`.
    - API `/surveys` trả về cấu trúc JSON với các nhóm, nhóm phụ, câu hỏi, và đáp án.

- **Sắp xếp ngẫu nhiên đáp án**:
    - Khi hiển thị câu hỏi trong SurveyJS, sử dụng hàm random để xáo trộn thứ tự `choices` trong giao diện.
    - Duy trì một ánh xạ (mapping) giữa `value` (a, b, c, d) và `score` (1, 2, 3, 4) để đảm bảo điểm số được ghi nhận đúng.

- **Tính điểm**:
    - Khi người dùng chọn đáp án (ví dụ: `value: "b"`), ánh xạ sang điểm số (2) dựa trên `Choice.score`.
    - Tổng hợp điểm số vào `totalScore_DL`, `totalScore_CN`, v.v., và lưu vào `SurveyResult`.

---

### Ví dụ minh họa cấu trúc dữ liệu trong Strapi
```json
{
  "collections": [
    {
      "name": "Survey",
      "fields": {
        "title": { "type": "string" },
        "description": { "type": "string" },
        "pages": { "type": "relation", "target": "QuestionGroup", "relationType": "oneToMany" },
        "calculatedValues": { "type": "json" },
        "completedHtmlOnCondition": { "type": "json" }
      }
    },
    {
      "name": "QuestionGroup",
      "fields": {
        "name": { "type": "string" },
        "title": { "type": "string", "example": "Dữ liệu" },
        "subGroups": { "type": "relation", "target": "SubGroup", "relationType": "oneToMany" },
        "survey": { "type": "relation", "target": "Survey", "relationType": "manyToOne" }
      }
    },
    {
      "name": "SubGroup",
      "fields": {
        "name": { "type": "string", "example": "Khung quản trị, chính sách, tiêu chuẩn dữ liệu" },
        "questions": { "type": "relation", "target": "Question", "relationType": "oneToMany" },
        "questionGroup": { "type": "relation", "target": "QuestionGroup", "relationType": "manyToOne" }
      }
    },
    {
      "name": "Question",
      "fields": {
        "name": { "type": "string", "example": "question1" },
        "title": { "type": "string" },
        "description": { "type": "string" },
        "type": { "type": "string", "example": "radiogroup" },
        "isRequired": { "type": "boolean" },
        "choices": { "type": "relation", "target": "Choice", "relationType": "oneToMany" },
        "subGroup": { "type": "relation", "target": "SubGroup", "relationType": "manyToOne" }
      }
    },
    {
      "name": "Choice",
      "fields": {
        "value": { "type": "string", "example": "a" },
        "text": { "type": "string" },
        "score": { "type": "integer", "example": 1 },
        "question": { "type": "relation", "target": "Question", "relationType": "manyToOne" }
      }
    },
    {
      "name": "UserInfo",
      "fields": {
        "name": { "type": "string" },
        "email": { "type": "string", "encrypted": true }
      }
    },
    {
      "name": "SurveyResult",
      "fields": {
        "result": { "type": "json" },
        "scores": { "type": "json", "example": { "question1Score": 2, "totalScore_DL": 2.5 } },
        "completedAt": { "type": "datetime" },
        "userInfo": { "type": "relation", "target": "UserInfo", "relationType": "manyToOne" }
      }
    }
  ]
}
```

---

### Ghi chú bổ sung
- **Sắp xếp ngẫu nhiên đáp án**: Đảm bảo rằng hàm random được áp dụng khi hiển thị `choices` trong SurveyJS, nhưng ánh xạ `value` sang `score` được thực hiện chính xác khi gửi dữ liệu.
- **Nhập dữ liệu vào Strapi**: Sử dụng script tự động hoặc giao diện admin để ánh xạ các trường từ `survey-data.json` sang các collection types, đặc biệt chú ý đến `calculatedValues` và `completedHtmlOnCondition`.
- **Bảo mật điểm số**: Đảm bảo rằng logic tính điểm được kiểm tra kỹ lưỡng trong E2E test để tránh lỗi ánh xạ điểm số khi đáp án được xáo trộn.

Đề bài đã được làm rõ và chi tiết hóa, tập trung vào quản lý câu hỏi, nhóm, nhóm phụ, và cơ chế tính điểm, đồng thời đảm bảo đáp án hiển thị ngẫu nhiên nhưng điểm số được bảo toàn. Nếu cần thêm chi tiết hoặc mã minh họa cụ thể, hãy yêu cầu!
