{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "import-survey-data": "node scripts/import-survey-data.js"}, "dependencies": {"@strapi/plugin-cloud": "5.13.1", "@strapi/plugin-users-permissions": "5.13.1", "@strapi/strapi": "5.13.1", "better-sqlite3": "11.3.0", "bcryptjs": "^2.4.3", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "3fa70fb7cd4f63ff225873e37fd0af325c3fb39168109e3e5e63317c455fb569"}}