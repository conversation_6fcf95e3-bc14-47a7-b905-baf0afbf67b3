const bcrypt = require('bcryptjs');

module.exports = {
  async beforeCreate(event) {
    const { data } = event.params;
    
    if (data.email) {
      // Hash the email for security
      const saltRounds = 10;
      data.emailHash = await bcrypt.hash(data.email, saltRounds);
    }
  },

  async beforeUpdate(event) {
    const { data } = event.params;
    
    if (data.email) {
      // Hash the email for security
      const saltRounds = 10;
      data.emailHash = await bcrypt.hash(data.email, saltRounds);
    }
  }
};
