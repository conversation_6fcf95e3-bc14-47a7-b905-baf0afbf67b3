{"kind": "collectionType", "collectionName": "user_infos", "info": {"singularName": "user-info", "pluralName": "user-infos", "displayName": "User Info", "description": "User information collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "email": {"type": "string", "required": true}, "emailHash": {"type": "string", "private": true}, "surveyResults": {"type": "relation", "relation": "oneToMany", "target": "api::survey-result.survey-result", "mappedBy": "userInfo"}}}