{"kind": "collectionType", "collectionName": "surveys", "info": {"singularName": "survey", "pluralName": "surveys", "displayName": "Survey", "description": "Survey collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "text"}, "locale": {"type": "string", "default": "vi"}, "completedHtml": {"type": "text"}, "completedHtmlOnCondition": {"type": "json"}, "calculatedValues": {"type": "json"}, "questionGroups": {"type": "relation", "relation": "oneToMany", "target": "api::question-group.question-group", "mappedBy": "survey"}}}