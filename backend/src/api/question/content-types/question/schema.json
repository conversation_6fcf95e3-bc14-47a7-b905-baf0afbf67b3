{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question", "description": "Question collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "title": {"type": "string", "required": true}, "description": {"type": "text"}, "type": {"type": "string", "required": true, "default": "radiogroup"}, "isRequired": {"type": "boolean", "default": true}, "subGroup": {"type": "relation", "relation": "manyToOne", "target": "api::sub-group.sub-group", "inversedBy": "questions"}, "choices": {"type": "relation", "relation": "oneToMany", "target": "api::choice.choice", "mappedBy": "question"}}}