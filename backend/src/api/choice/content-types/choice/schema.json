{"kind": "collectionType", "collectionName": "choices", "info": {"singularName": "choice", "pluralName": "choices", "displayName": "Choice", "description": "Choice collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"value": {"type": "string", "required": true}, "text": {"type": "string", "required": true}, "score": {"type": "integer", "required": true, "min": 1, "max": 4}, "question": {"type": "relation", "relation": "manyToOne", "target": "api::question.question", "inversedBy": "choices"}}}