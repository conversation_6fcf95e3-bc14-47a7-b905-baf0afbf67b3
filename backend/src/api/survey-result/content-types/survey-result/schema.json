{"kind": "collectionType", "collectionName": "survey_results", "info": {"singularName": "survey-result", "pluralName": "survey-results", "displayName": "Survey Result", "description": "Survey result collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"result": {"type": "json", "required": true}, "scores": {"type": "json", "required": true}, "completedAt": {"type": "datetime", "required": true}, "userInfo": {"type": "relation", "relation": "manyToOne", "target": "api::user-info.user-info", "inversedBy": "surveyResults"}}}