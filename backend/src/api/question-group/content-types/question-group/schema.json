{"kind": "collectionType", "collectionName": "question_groups", "info": {"singularName": "question-group", "pluralName": "question-groups", "displayName": "Question Group", "description": "Question group collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "title": {"type": "string", "required": true}, "survey": {"type": "relation", "relation": "manyToOne", "target": "api::survey.survey", "inversedBy": "questionGroups"}, "subGroups": {"type": "relation", "relation": "oneToMany", "target": "api::sub-group.sub-group", "mappedBy": "questionGroup"}}}