{"kind": "collectionType", "collectionName": "sub_groups", "info": {"singularName": "sub-group", "pluralName": "sub-groups", "displayName": "Sub Group", "description": "Sub group collection type"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "questionGroup": {"type": "relation", "relation": "manyToOne", "target": "api::question-group.question-group", "inversedBy": "subGroups"}, "questions": {"type": "relation", "relation": "oneToMany", "target": "api::question.question", "mappedBy": "subGroup"}}}