const fs = require('fs');
const path = require('path');

// Import survey data from JSON file to Strapi
async function importSurveyData() {
  try {
    console.log('🚀 Starting survey data import...');

    // Read the survey data file
    const surveyDataPath = path.join(__dirname, '../../survey-data.json');

    if (!fs.existsSync(surveyDataPath)) {
      throw new Error('survey-data.json file not found');
    }

    const surveyData = JSON.parse(fs.readFileSync(surveyDataPath, 'utf8'));
    console.log('📖 Survey data loaded successfully');

    // Initialize Strapi
    const Strapi = require('@strapi/strapi');
    const strapi = Strapi();
    await strapi.load();

    console.log('🔌 Strapi loaded successfully');

    // Clear existing data
    console.log('🧹 Clearing existing survey data...');
    await clearExistingData(strapi);

    // Create survey
    console.log('📝 Creating survey...');
    const survey = await createSurvey(strapi, surveyData);

    // Create question groups, subgroups, questions, and choices
    console.log('📋 Creating question groups and questions...');
    await createQuestionStructure(strapi, survey, surveyData);

    console.log('✅ Survey data import completed successfully!');

    await strapi.destroy();
    process.exit(0);

  } catch (error) {
    console.error('❌ Error importing survey data:', error);
    process.exit(1);
  }
}

async function clearExistingData(strapi) {
  // Clear in reverse order due to relationships
  await strapi.entityService.deleteMany('api::choice.choice', {});
  await strapi.entityService.deleteMany('api::question.question', {});
  await strapi.entityService.deleteMany('api::sub-group.sub-group', {});
  await strapi.entityService.deleteMany('api::question-group.question-group', {});
  await strapi.entityService.deleteMany('api::survey.survey', {});
}

async function createSurvey(strapi, surveyData) {
  const survey = await strapi.entityService.create('api::survey.survey', {
    data: {
      title: surveyData.title?.vi || surveyData.title?.default || 'Khảo sát AI',
      description: surveyData.description || '',
      locale: surveyData.locale || 'vi',
      completedHtml: surveyData.completedHtml || '',
      completedHtmlOnCondition: surveyData.completedHtmlOnCondition || [],
      calculatedValues: surveyData.calculatedValues || []
    }
  });

  console.log(`✅ Created survey: ${survey.title}`);
  return survey;
}

async function createQuestionStructure(strapi, survey, surveyData) {
  if (!surveyData.pages || !Array.isArray(surveyData.pages)) {
    throw new Error('Invalid survey data: pages not found');
  }

  for (const page of surveyData.pages) {
    // Create question group (page)
    const questionGroup = await strapi.entityService.create('api::question-group.question-group', {
      data: {
        name: page.name,
        title: page.title?.vi || page.title?.default || page.name,
        survey: survey.id
      }
    });

    console.log(`📁 Created question group: ${questionGroup.title}`);

    if (!page.elements || !Array.isArray(page.elements)) {
      continue;
    }

    // Group questions by description (subgroup)
    const subGroups = new Map();

    for (const element of page.elements) {
      if (element.type !== 'radiogroup') continue;

      const subGroupName = element.description?.vi || element.description?.default || 'Default';

      if (!subGroups.has(subGroupName)) {
        const subGroup = await strapi.entityService.create('api::sub-group.sub-group', {
          data: {
            name: subGroupName,
            questionGroup: questionGroup.id
          }
        });
        subGroups.set(subGroupName, subGroup);
        console.log(`📂 Created sub group: ${subGroupName}`);
      }

      const subGroup = subGroups.get(subGroupName);

      // Create question
      const question = await strapi.entityService.create('api::question.question', {
        data: {
          name: element.name,
          title: element.title?.vi || element.title?.default || element.title,
          description: element.description?.vi || element.description?.default || '',
          type: element.type,
          isRequired: element.isRequired || false,
          subGroup: subGroup.id
        }
      });

      console.log(`❓ Created question: ${question.name}`);

      // Create choices
      if (element.choices && Array.isArray(element.choices)) {
        for (const choice of element.choices) {
          const score = getScoreForChoice(choice.value);

          await strapi.entityService.create('api::choice.choice', {
            data: {
              value: choice.value,
              text: choice.text?.vi || choice.text?.default || choice.text,
              score: score,
              question: question.id
            }
          });
        }
        console.log(`✅ Created ${element.choices.length} choices for question: ${question.name}`);
      }
    }
  }
}

function getScoreForChoice(value) {
  // Map choice values to scores as per requirements
  const scoreMap = {
    'a': 1,
    'b': 2,
    'c': 3,
    'd': 4
  };

  return scoreMap[value] || 1;
}

// Run the import
importSurveyData();
