const axios = require('axios');

const API_BASE = 'http://127.0.0.1:1337/api';
const ADMIN_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzQ4MzE4ODkzLCJleHAiOjE3NTA5MTA4OTN9.goqhjHRUnIn8plFyLclqeslKjc58pqvfwgBrLzYeA1o';

// Configure axios with auth header
axios.defaults.headers.common['Authorization'] = `Bearer ${ADMIN_TOKEN}`;

async function createSampleData() {
  try {
    console.log('🚀 Creating sample survey data...');

    // Create a simple survey
    const surveyResponse = await axios.post(`${API_BASE}/surveys`, {
      data: {
        title: 'Khảo sát AI',
        description: 'Khảo sát đánh gi<PERSON> mức độ sẵn sàng AI của doanh nghiệp',
        locale: 'vi',
        completedHtml: 'Cảm ơn bạn đã hoàn thành khảo sát!',
        completedHtmlOnCondition: [
          {
            expression: '{totalScore} > 3',
            html: 'Doanh nghiệp của bạn đã sẵn sàng cho AI!'
          },
          {
            expression: '{totalScore} <= 3',
            html: 'Doanh nghiệp của bạn cần chuẩn bị thêm cho AI.'
          }
        ],
        calculatedValues: [
          {
            name: 'totalScore',
            expression: '({question1Score} + {question2Score}) / 2'
          }
        ]
      }
    });

    const survey = surveyResponse.data.data;
    console.log('✅ Created survey:', survey.attributes.title);

    // Create question group
    const groupResponse = await axios.post(`${API_BASE}/question-groups`, {
      data: {
        name: 'data-group',
        title: 'Dữ liệu',
        survey: survey.id
      }
    });

    const group = groupResponse.data.data;
    console.log('✅ Created question group:', group.attributes.title);

    // Create sub group
    const subGroupResponse = await axios.post(`${API_BASE}/sub-groups`, {
      data: {
        name: 'Quản trị dữ liệu',
        questionGroup: group.id
      }
    });

    const subGroup = subGroupResponse.data.data;
    console.log('✅ Created sub group:', subGroup.attributes.name);

    // Create questions
    const question1Response = await axios.post(`${API_BASE}/questions`, {
      data: {
        name: 'question1',
        title: 'Doanh nghiệp đã có khung quản trị dữ liệu chính thức chưa?',
        description: 'Khung quản trị, chính sách, tiêu chuẩn dữ liệu',
        type: 'radiogroup',
        isRequired: true,
        subGroup: subGroup.id
      }
    });

    const question1 = question1Response.data.data;
    console.log('✅ Created question 1:', question1.attributes.title);

    const question2Response = await axios.post(`${API_BASE}/questions`, {
      data: {
        name: 'question2',
        title: 'Doanh nghiệp có quy trình kiểm soát chất lượng dữ liệu không?',
        description: 'Quy trình kiểm soát chất lượng dữ liệu',
        type: 'radiogroup',
        isRequired: true,
        subGroup: subGroup.id
      }
    });

    const question2 = question2Response.data.data;
    console.log('✅ Created question 2:', question2.attributes.title);

    // Create choices for question 1
    const choices1 = [
      { value: 'a', text: 'Chưa có', score: 1 },
      { value: 'b', text: 'Đang xây dựng', score: 2 },
      { value: 'c', text: 'Có nhưng chưa triển khai đầy đủ', score: 3 },
      { value: 'd', text: 'Có và đã triển khai hiệu quả', score: 4 }
    ];

    for (const choice of choices1) {
      await axios.post(`${API_BASE}/choices`, {
        data: {
          ...choice,
          question: question1.id
        }
      });
    }

    // Create choices for question 2
    const choices2 = [
      { value: 'a', text: 'Không có', score: 1 },
      { value: 'b', text: 'Có nhưng không thường xuyên', score: 2 },
      { value: 'c', text: 'Có và thực hiện định kỳ', score: 3 },
      { value: 'd', text: 'Có hệ thống tự động kiểm soát', score: 4 }
    ];

    for (const choice of choices2) {
      await axios.post(`${API_BASE}/choices`, {
        data: {
          ...choice,
          question: question2.id
        }
      });
    }

    console.log('✅ Created all choices');
    console.log('🎉 Sample data created successfully!');

  } catch (error) {
    console.error('❌ Error creating sample data:', error.response?.data || error.message);
  }
}

createSampleData();
