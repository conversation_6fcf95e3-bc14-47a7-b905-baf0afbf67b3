{"locale": "vi", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Khảo sát câu 1", "completedHtml": "Cảm ơn bạn đã hoàn thành khảo sát!<br/>Tổng điểm trung bình mảng Chiến lược và Lãnh đạo: {totalScore_CLVLD}<br/>Tổng điểm trung bình mảng Dữ liệu: {totalScore_DL}<br/>Tổng điểm trung bình mảng Công nghệ: {totalScore_CN}<br/>Tổng điểm trung bình mảng Con người, <PERSON><PERSON><PERSON> hó<PERSON>, T<PERSON> chức: {totalScore_CNVHTO}<br/>Tổng điểm trung bình mảng Quản trị và Vận hành: {totalScore_QTVVH}<br/><b>Bạn được hạng D</b>", "completedHtmlOnCondition": [{"expression": "({totalScore_CLVLD} > 3.4) and ({totalScore_CN} > 3.6) and ({totalScore_DL} > 3.6) and ({totalScore_CNVHTO} > 3.4) and ({totalScore_QTVVH} > 3.4)", "html": "<p><PERSON><PERSON><PERSON> nghiệp đã tích hợp trí tuệ nhân tạo (AI) một cách sâu rộng và có chiến lược vào toàn bộ hoạt động và định hướng phát triển.AI không chỉ là công cụ hỗ trợ, mà đã trở thành yếu tố cốt lõi thúc đẩy đổi mới, hiệu quả vận hành và tạo lợi thế cạnh tranh bền vững.</p><ul><li>🔹 Lãnh đạo cấp cao cam kết mạnh mẽ, chủ động dẫn dắt chiến lược AI gắn với mục tiêu dài hạn.</li><li>🔹 Các mô hình AI được triển khai ổn định, quy mô lớn, tích hợp liền mạch vào quy trình kinh doanh cốt lõi.</li><li>🔹 Hạ tầng công nghệ hiện đại, linh hoạt và an toàn, đủ khả năng mở rộng và tối ưu hiệu suất AI.</li><li>🔹 Dữ liệu được quản lý như một tài sản chiến lược, với hệ thống quản trị chất lượng, quyền riêng tư và truy xuất nguồn gốc rõ ràng.</li><li>🔹 Văn hóa tổ chức hỗ trợ đổi mới, học hỏi liên tục, và sẵn sàng thích nghi với những thay đổi do AI mang lại.</li><li>🔹 Doanh nghiệp không chỉ sử dụng AI để tối ưu, mà còn để định hình lại mô hình kinh doanh, khám phá sản phẩm mới, và mở rộng thị trường.</li></ul>"}, {"expression": "({totalScore_CLVLD} > 2.9) and ({totalScore_CN} > 2.9) and ({totalScore_DL} > 2.9) and ({totalScore_CNVHTO} > 2.4) and ({totalScore_QTVVH} > 2.4)", "html": "<p><PERSON><PERSON><PERSON> nghiệp đã tích hợp AI vào nhiều quy trình kinh doanh và đảm bảo sự liên kết chặt chẽ giữa chiến lược AI và chiến lược tổ chức. AI được xem là công cụ then chốt để cải thiện hiệu suất, nâng cao trải nghiệm khách hàng và hỗ trợ ra quyết định.</p><ul><li>🔹 Chiến lược AI rõ ràng, có định hướng và được cập nhật định kỳ.</li><li>🔹 Các phòng ban đã bắt đầu hợp tác hiệu quả để triển khai AI ở quy mô chức năng hoặc đơn vị.</li><li>🔹 Hạ tầng công nghệ tương đối hiện đại, hỗ trợ triển khai các mô hình AI phức tạp.</li><li>🔹 Đội ngũ nội bộ có kỹ năng phân tích dữ liệu và áp dụng mô hình AI trong công việc hàng ngày.</li><li>🔹 Các vấn đề về đạo đức, bảo mật, và tuân thủ trong triển khai AI được nhận diện và quản lý có hệ thống.</li><li>🔹 Tổ chức bắt đầu có hệ thống đo lường hiệu quả tương đối rõ</li></ul>"}, {"expression": "({totalScore_CLVLD} > 1.9) and ({totalScore_CN} > 1.9) and ({totalScore_DL} > 1.9) and ({totalScore_CNVHTO} > 1.4) and ({totalScore_QTVVH} > 1.4)", "html": "<p><PERSON><PERSON><PERSON> nghiệp đã có những bước đi bài bản và có cấu trúc trong việc tiếp cận AI, dù ứng dụng thực tế còn giới hạn về quy mô và tác động. Tổ chức đang tập trung xây dựng năng lực nội bộ và xác định các trường hợp sử dụng AI khả thi.</p><ul><li>🔹 Có nhóm chuyên trách dữ liệu hoặc AI, hoạt động tương đối ổn định.</li><li>🔹 Đã triển khai một số dự án AI thử nghiệm (pilot) ở cấp phòng ban hoặc quy trình cụ thể.</li><li>🔹 Hạ tầng công nghệ đang trong quá trình nâng cấp, c<PERSON> khả năng xử lý dữ liệu và hỗ trợ học máy cơ bản.</li><li>🔹 <PERSON>ó quy trình thu thập và quản lý dữ liệu, nhưng chất lượng và mức độ chuẩn hóa còn chưa đồng đều.</li><li>🔹 Nhận thức nội bộ về AI đang được nâng cao thông qua đào tạo, hội thảo và các chương trình chuyển giao kiến thức.</li><li>🔹 Chưa có chiến lược AI rõ ràng ở cấp tổ chức, nhưng có định hướng phát triển</li></ul>"}, {"expression": "{totalScore_CLVLD} < 2 or {totalScore_CN} < 2 or {totalScore_DL} < 2 or {totalScore_CNVHTO} < 1.5 or {totalScore_QTVVH} < 1.5", "html": "<p><PERSON><PERSON><PERSON> nghiệp đang ở giai đoạn khởi đầu trong hành trình AI. <PERSON>h<PERSON>n thức về tiềm năng của AI đã xuất hiện, nhưng chưa có sự chuẩn bị hệ thống về công nghệ, con người hoặc chiến lược.</p><ul><li>🔹 Chưa có chiến lược AI chính thức.</li><li>🔹 Việc ứng dụng AI chủ yếu mang tính thử nghiệm, nhỏ lẻ hoặc do bên ngoài cung cấp.</li><li>🔹 Thiếu hụt về kỹ năng dữ liệu và AI trong nội bộ.</li><li>🔹 Cơ sở dữ liệu phân tán, chất lượng chưa được kiểm soát tốt, hạ tầng công nghệ chưa đáp ứng nhu cầu AI.</li><li>🔹 Lãnh đạo nhận thức được vai trò của AI, nhưng chưa cam kết đầu tư mạnh mẽ hoặc dài hạn.</li><li>🔹 Tổ chức chưa xác định rõ giá trị mà AI có thể mang lại.</li></ul>"}], "pages": [{"name": "page1", "title": {"vi": "<PERSON><PERSON> liệu"}, "elements": [{"type": "radiogroup", "name": "question1", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON> nghiệp đã có khung quản trị dữ liệu ch<PERSON>h thức chưa (VD: ch<PERSON><PERSON> sách phân quyền, ch<PERSON><PERSON>, phân loại dữ liệu…)?"}, "description": {"vi": "<PERSON><PERSON><PERSON> quản trị, <PERSON><PERSON><PERSON>, ti<PERSON><PERSON> chuẩn dữ liệu -\n<PERSON>hung quản trị dữ liệu"}, "setValueIf": "{question1} allof [a]", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Ch<PERSON><PERSON> có khung quản trị hoặc chỉ ở mức văn bản sơ khai, chưa á<PERSON> dụng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> khung quản trị ban đầu, <PERSON><PERSON> dụng một phần trong các đơn vị"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> khung quản trị rõ ràng, <PERSON><PERSON><PERSON><PERSON> ban hành và áp dụng tương đối đồng bộ trong tổ chức"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> quản trị dữ liệu đ<PERSON>y đủ, cậ<PERSON> nh<PERSON><PERSON> đ<PERSON><PERSON>, t<PERSON><PERSON> hợ<PERSON> v<PERSON><PERSON> chi<PERSON><PERSON> lư<PERSON><PERSON> AI và vận hành số"}}]}, {"type": "radiogroup", "name": "question7", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON> định nghĩa rõ ràng về các tiêu chuẩn dữ liệu, đ<PERSON><PERSON> dạng, thu<PERSON><PERSON> t<PERSON><PERSON> bắ<PERSON> buộc cho từng loại dữ liệu không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> quản trị, <PERSON><PERSON><PERSON>, tiê<PERSON> chuẩn dữ liệu -\n<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>, tiêu chuẩn dữ liệu trong doanh nghiệp"}, "setValueIf": "{question7} allof [a]", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON>ông có tiêu chuẩn hoặc chỉ áp dụng không ch<PERSON>h thức"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> ngh<PERSON> sơ bộ, <PERSON><PERSON> dụng cho một số loại dữ liệu quan trọng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> hệ thống tiêu chuẩn hoá định dạng, thu<PERSON><PERSON> t<PERSON>h dữ liệu theo từng danh mục cụ thể"}}, {"value": "d", "text": {"vi": "Ti<PERSON><PERSON> chuẩn dữ liệu thống nh<PERSON>t, gắn với ch<PERSON>h sách chất lượng và chia sẻ dữ liệu trong hệ sinh thái"}}]}, {"type": "radiogroup", "name": "question6", "title": {"default": "Câu hỏi 1", "vi": "<PERSON>ó quy trình kiểm so<PERSON>t việ<PERSON> nhân bản, sử dụng lại hoặc thay đổi dữ liệu gốc không?"}, "description": {"vi": "<PERSON><PERSON><PERSON>n trị vòng đời dữ liệu - <PERSON>uy trình tái sử dụng, can thiệp vào dữ liệu"}, "setValueIf": "{question6} allof [a]", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> kiể<PERSON>, dữ liệu bị trùng lặp hoặc sửa đổi không theo dõi"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> quy trình kiểm soát cơ bản cho một số loại dữ liệu quan trọng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> ch<PERSON>h sách kiểm soát thay đổi, l<PERSON><PERSON> vết cho hầu hết dữ liệu vận hành"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> so<PERSON>t chặt chẽ với hệ thống quản trị phiên bản, quyền chỉnh sửa và theo dõi vòng đời từng bản ghi dữ liệu"}}]}, {"type": "radiogroup", "name": "question5", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON> tích hợp quản lý dữ liệu vào các hệ thống nghiệp vụ để đảm bảo t<PERSON>h đồng bộ"}, "description": {"vi": "<PERSON>u<PERSON>n trị vòng đời dữ liệu - <PERSON><PERSON><PERSON> hợp vào hệ thống quy trình của do<PERSON>h nghiệp"}, "setValueIf": "{question5} allof [a]", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> thống dữ liệu rờ<PERSON> r<PERSON>, nhi<PERSON><PERSON> nguồn không liên kết"}}, {"value": "b", "text": {"vi": "Một số hệ thống nghiệp vụ đã đồng bộ dữ liệu theo hướng thủ công"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> <PERSON><PERSON> hệ thống đã kết nối, chia sẻ dữ liệu thông qua tích hợp trung gian hoặc nền tảng dữ liệu chung"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> liệu vận hành được tích hợp chặt chẽ với tất cả hệ thống lõi (ERP, CRM, BI…), hỗ tr<PERSON> <PERSON> khai thác tức thời và chính xác"}}]}, {"type": "radiogroup", "name": "question4", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON> thực hiện kiểm tra định kỳ các chỉ số chất lượng dữ liệu (đ<PERSON><PERSON> đủ, nhất quán, ch<PERSON><PERSON> xác, hợ<PERSON> lệ)?"}, "description": {"vi": "<PERSON><PERSON><PERSON> bảo chất lượng dữ liệu - <PERSON><PERSON><PERSON> tra định kỳ về chất lượng dữ liệu"}, "setValueIf": "{question4} allof [a]", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> thực hiện / chỉ khi có sự cố"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> kiểm tra thủ công đ<PERSON>nh kỳ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> tra tự động định kỳ theo chỉ số chất lượng"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> hợp dashboard chất lư<PERSON><PERSON> theo thời gian thực, chủ động cải thiện"}}]}, {"type": "radiogroup", "name": "question3", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON> nguồn dữ liệu nội bộ có đư<PERSON><PERSON> tích hợp về một nền tảng chung không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> tảng cho dữ liệu - Tập trung hóa dữ liệu"}, "setValueIf": "{question3} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> liệu <PERSON>, <PERSON><PERSON><PERSON><PERSON> chu<PERSON><PERSON> hóa"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON>t đầu gom về các kho dữ liệu cục bộ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> / kho dữ liệu tích hợp"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON>n tảng dữ liệu tổng thể, quản trị chặt chẽ, chuẩ<PERSON> hóa cho mọi bộ phận"}}]}, {"type": "radiogroup", "name": "question2", "title": {"default": "Câu hỏi 1", "vi": "<PERSON>ó công cụ quản lý metadata, catalog dữ liệu để tra cứu và truy xuất dễ dàng không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> tảng cho dữ liệu - <PERSON><PERSON><PERSON>n lý metadata"}, "setValueIf": "{question2} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có hoặc chưa dùng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> trì thủ công một phần thông tin metadata"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> công cụ catalog và tìm kiếm c<PERSON> bản"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> kế<PERSON> h<PERSON> thống, tra c<PERSON>u dễ dàng"}}]}, {"type": "radiogroup", "name": "question8", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON><PERSON> dùng (bao gồ<PERSON> kỹ thuật và nghiệp vụ) có thể tự tra cứu và khai thác dữ liệu theo phân quyền không?"}, "description": {"vi": "Sẵn sàng truy cập và khai thác - <PERSON><PERSON><PERSON> năng tự tiếp cận và khai thác dữ liệu"}, "setValueIf": "{question8} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> thu<PERSON>c hoàn toàn vào IT"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> yêu cầu mới để cấp truy cập"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> quy<PERSON>, ng<PERSON><PERSON><PERSON> dùng nghiệ<PERSON> vụ truy xuất đ<PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> self-service theo vai trò, trực quan"}}]}, {"type": "radiogroup", "name": "question9", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON> độ bảo mật và phân quyền có gây cản trở quá mức cho truy cập dữ liệu không?"}, "description": {"vi": "Sẵn sàng truy cập và khai thác - T<PERSON>c động của an ninh bảo mật"}, "setValueIf": "{question9} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> quyền không rõ ràng, g<PERSON><PERSON> xung đột"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> gi<PERSON>i hạn truy cập nh<PERSON>ng linh hoạt chưa cao"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> s<PERSON>ch rõ ràng, an toàn nhưng hiệu quả"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> mật cấp độ chi tiết, <PERSON><PERSON><PERSON><PERSON> cản trở thao tác hợp lý"}}]}, {"type": "radiogroup", "name": "question10", "title": {"default": "Câu hỏi 1", "vi": "Có tài liệu mô tả dữ liệu đầy đủ không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> sâu về dữ liệu - <PERSON>ô tả dữ liệu"}, "setValueIf": "{question10} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>ng có tài li<PERSON>u, thông tin phân tán"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> mô tả sơ lược cho một số dữ liệu ch<PERSON>h"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> hệ thống documentation và lineage tương đối đầy đủ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> cập nhật tự động, dễ tìm kiếm và kiểm tra"}}]}, {"type": "radiogroup", "name": "question11", "title": {"default": "Câu hỏi 1", "vi": "<PERSON><PERSON><PERSON> bộ phận nghiệp vụ có thể đặt câu hỏi và hiểu được dữ liệu mà AI đang sử dụng không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> sâu về dữ liệu - Hiểu dữ liệu mà AI sử dụng để phân tích"}, "setValueIf": "{question11} empty", "setValueExpression": "10", "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> biết AI dùng dữ liệu gì"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> trao đổi không ch<PERSON>h thức"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> báo cáo rõ dữ liệu AI đang sử dụng"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> <PERSON>, logic AI <PERSON><PERSON><PERSON><PERSON> vụ hiểu và tham gia phản biện"}}]}]}, {"name": "page2", "title": {"vi": "<PERSON><PERSON><PERSON> nghệ"}, "elements": [{"type": "radiogroup", "name": "question12", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã xây dựng hệ thống lưu trữ dữ liệu như thế nào?"}, "description": {"vi": "Kiến trúc dữ liệu và đám mây - <PERSON><PERSON>n tảng lưu trữ dữ liệu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> tr<PERSON> cụ<PERSON> bộ, thi<PERSON><PERSON> chu<PERSON>n"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> kho dữ liệu cục bộ hoặc server nội bộ tập trung"}}, {"value": "c", "text": {"vi": "Có data warehouse/data lake, kiến trúc có tổ chức"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> hi<PERSON>, ph<PERSON> h<PERSON>"}}]}, {"type": "radiogroup", "name": "question13", "title": {"vi": "<PERSON><PERSON><PERSON> d<PERSON> vụ AI/ML trên đ<PERSON> (AWS Sagemaker, Azure ML, Google Vertex AI…) đã được thử nghiệm/triển khai chưa?"}, "description": {"vi": "<PERSON><PERSON>n trúc dữ liệu và đám mây - <PERSON><PERSON><PERSON> dịch vụ AI trên đám mây"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> từng thử nghiệm"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> thử nghiệm nhưng chưa phổ biến"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> triển khai một số dịch v<PERSON> <PERSON>n định"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON>, khai thác hiệu quả AI/ML cloud-native"}}]}, {"type": "radiogroup", "name": "question14", "title": {"vi": "Có chiến lược rõ ràng cho việc di chuyển dữ liệu lên đám mây không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> trúc dữ liệu và đám mây - <PERSON><PERSON> hoạch move to cloud"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>ng có hoặc chưa rõ"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> xây dựng hoặc thí điểm"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> chiến lư<PERSON><PERSON> rõ ràng và từng b<PERSON><PERSON><PERSON> thực hiện"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON>n bộ kiến trúc h<PERSON>ớ<PERSON> cloud, tối <PERSON>u hiệu n<PERSON>ng – b<PERSON><PERSON> mật – chi phí"}}]}, {"type": "radiogroup", "name": "question15", "title": {"vi": "<PERSON><PERSON><PERSON> độ an toàn, phân quyền truy cập và mã hóa dữ liệu trên cloud đư<PERSON><PERSON> quản lý thế nào?"}, "description": {"vi": "Kiến trúc dữ liệu và đám mây - Quản trị vận hành trên đám mây"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> áp dụng / chưa đầy đủ"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> cơ bản về phân quyền và mã hóa"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON>n trị an toàn theo vai trò và dịch vụ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON><PERSON> mậ<PERSON>, <PERSON><PERSON><PERSON><PERSON>, tu<PERSON> thủ chặt chẽ (VD: GDPR, ISO 27001...)"}}]}, {"type": "radiogroup", "name": "question16", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có sử dụng các công cụ phân tích/phát triển dữ liệu như Jupyter Notebook, RStudio, Databricks..."}, "description": {"vi": "<PERSON><PERSON>ng cụ và nền tảng AI - <PERSON><PERSON><PERSON> học dữ liệu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có hoặc dùng cục bộ, rời rạc"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> sử dụng nhưng thiếu chuẩn hóa"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> công cụ được dùng phổ biến, hỗ trợ nhóm <PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> hóa toàn tổ chức cho mô hình AI"}}]}, {"type": "radiogroup", "name": "question17", "title": {"vi": "<PERSON><PERSON><PERSON> công cụ AI có được chuẩn hóa trong tổ chức hay triển khai theo kiểu rời rạc từng nhóm?"}, "description": {"vi": "<PERSON><PERSON>ng cụ và nền tảng AI - <PERSON><PERSON><PERSON> hóa công cụ"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Mỗi nhóm dùng riêng, không chia sẻ"}}, {"value": "b", "text": {"vi": "Một vài công cụ dùng chung nhưng chưa thống nhất"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> h<PERSON> rõ ràng"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> hóa toàn bộ công cụ, đ<PERSON><PERSON> tạo nội bộ, có quy trình sử dụng"}}]}, {"type": "radiogroup", "name": "question18", "title": {"vi": "<PERSON><PERSON><PERSON> công cụ hiện tại có tương thích với chu trình xử lý dữ liệu và mô hình AI không?"}, "description": {"vi": "Công cụ và nền tảng AI - Tư<PERSON>ng thích với việc xử lý dữ liệu và mô hình AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> tương thích hoặc xử lý thủ công"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> một số chu trình thử nghiệm"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> hợ<PERSON> tư<PERSON><PERSON> đ<PERSON>i <PERSON>n đ<PERSON>nh"}}, {"value": "d", "text": {"vi": "<PERSON> trình xử lý dữ liệu + AI tự động hóa, g<PERSON><PERSON><PERSON> s<PERSON>t thời gian thực"}}]}, {"type": "radiogroup", "name": "question19", "title": {"vi": "<PERSON><PERSON><PERSON> hệ thống nghiệp vụ hiện tại có API để kết nối với bên ngoài không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> hợp hệ thống cũ - <PERSON> của hệ thống hiện tại"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có hoặc rất hạn chế"}}, {"value": "b", "text": {"vi": "Một số hệ thống có API nhưng không đồng bộ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> s<PERSON> hệ thống hỗ trợ <PERSON> chuẩn"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> thống hóa toàn bộ API, có cổng API Gateway quản lý"}}]}, {"type": "radiogroup", "name": "question20", "title": {"vi": "<PERSON>ữ liệu từ nhiều hệ thống có đư<PERSON><PERSON> đồng bộ hóa, hợ<PERSON> nhất theo thời gian gần thực (near real-time) không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> hợ<PERSON> hệ thống cũ - <PERSON><PERSON><PERSON> bộ dữ liệu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>, chậm tr<PERSON>"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON>ng bộ theo lô (batch) đ<PERSON><PERSON> kỳ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> như real-time một phần"}}, {"value": "d", "text": {"vi": "<PERSON>ần nh<PERSON> toàn bộ tích hợp real-time"}}]}, {"type": "radiogroup", "name": "question21", "title": {"vi": "<PERSON>ệ thống hiện tại có khả năng mở rộng (scalable) để phục vụ khối lượng dữ liệu lớn không?"}, "description": {"vi": "Cơ sở hạ tầng cho dữ liệu và AI - <PERSON><PERSON><PERSON> năng mở rộng của hệ thống cho dữ liệu lớn"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>, t<PERSON><PERSON> tải dễ nghẽn"}}, {"value": "b", "text": {"vi": "Mở rộng bằng tay, khó mở rộng ngang"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> thể mở rộng với hạ tầng ảo hóa"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> kế hướng scalable, auto-scaling và container hóa"}}]}, {"type": "radiogroup", "name": "question22", "title": {"vi": "<PERSON><PERSON> sử dụng nền tảng cloud computing (AWS, Azure, GCP…) để mở rộng năng lực tính toán khi cần không?"}, "description": {"vi": "<PERSON>ơ sở hạ tầng cho dữ liệu và AI - <PERSON><PERSON><PERSON> năng sử dụng điện toán đám mấy khi cần xử lý dữ liệu lớn"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> d<PERSON>"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> thử nghiệm các cloud service"}}, {"value": "c", "text": {"vi": "Dùng cloud hybrid để tăng năng lực khi cần"}}, {"value": "d", "text": {"vi": "Tự động hóa mở rộng qua cloud-native"}}]}, {"type": "radiogroup", "name": "question23", "title": {"vi": "<PERSON><PERSON> ch<PERSON>h sách kiểm soát truy cập đối với tài nguyên AI (d<PERSON> li<PERSON><PERSON>, mô hình, API) không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> mậ<PERSON>, tin cậy - <PERSON><PERSON><PERSON> so<PERSON>t truy cập"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> rõ ràng, thi<PERSON><PERSON> phân quyền"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> phân quyền thủ công hoặc theo nhóm"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> bi<PERSON>n pháp bảo mật đ<PERSON><PERSON><PERSON> áp dụng bài bản"}}, {"value": "d", "text": {"vi": "<PERSON>, g<PERSON><PERSON><PERSON> s<PERSON><PERSON>, audit log cho tài nguyên AI"}}]}, {"type": "radiogroup", "name": "question24", "title": {"vi": "<PERSON><PERSON>, quy <PERSON><PERSON><PERSON>, k<PERSON><PERSON> bản để phòng chố<PERSON>, xử lý các đe dọa tấn công mạng không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> mậ<PERSON>, tin cậy - <PERSON><PERSON><PERSON> tổ chức có một kế hoạch phát hiện đe doạ mạng"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON>hông có hoặc không cập nhật"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> nhưng thực hiện không thườ<PERSON> xuyên"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> k<PERSON> bản và diễn tập đ<PERSON><PERSON> k<PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> nhật liên tục và ứng phó tự động"}}]}, {"type": "radiogroup", "name": "question25", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có các giải pháp để theo dõi liên tục và thường xuyên các mối nguy hại về tấn công mạng không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> mật, tin cậy - Tổ chức có các biện pháp bảo mật liên tục theo dõi và phản hồi nhanh chóng các cuộc tấn công mạng"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON>h<PERSON><PERSON> theo dõi hoặc phản ứng muộn"}}, {"value": "b", "text": {"vi": "<PERSON> thủ công hoặc định kỳ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> công cụ giám sát c<PERSON> bản"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t th<PERSON>i gian <PERSON>, <PERSON><PERSON><PERSON> tích hành vi, <PERSON> chống tấn công"}}]}, {"type": "radiogroup", "name": "question26", "title": {"vi": "<PERSON><PERSON> phương án phục hồi sau thảm họa (disaster recovery) cho hệ thống không?"}, "description": {"vi": "<PERSON><PERSON><PERSON>, tin cậy - <PERSON><PERSON><PERSON> hồi sau thảm họa"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Không có hoặc không khả thi"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> bản sao l<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hồ<PERSON> ch<PERSON>m"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> hệ thống dự phòng nóng/lạnh"}}, {"value": "d", "text": {"vi": "DR site sẵn sàng, thời gian phục hồi trong thời gian ng<PERSON>n"}}]}]}, {"name": "page3", "title": {"vi": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>"}, "elements": [{"type": "radiogroup", "name": "question27", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã có chính sách và tiến hành tuyển dụng các nhân sự chuyên sâu về AI (Data scientist, k<PERSON> sư ML…)"}, "description": {"vi": "<PERSON><PERSON>à khoa học dữ liệu và kỹ sư ML - <PERSON><PERSON><PERSON> sách cho tuyển dụng và bồi dưỡng nhân sự này"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có hoặc thuê ngoài tạm thời"}}, {"value": "b", "text": {"vi": "<PERSON>ã có vài vị trí AI, chủ yếu hỗ trợ dự án"}}, {"value": "c", "text": {"vi": "Có team AI nội bộ ổn định"}}, {"value": "d", "text": {"vi": "Có team AI nội bộ ổn định"}}]}, {"type": "radiogroup", "name": "question28", "title": {"vi": "Tỷ lệ nhân sự có thể nghiên c<PERSON>, <PERSON><PERSON>, triể<PERSON> <PERSON>hai <PERSON> là bao nhiêu?"}, "description": {"vi": "<PERSON><PERSON><PERSON> khoa học dữ liệu và kỹ sư ML - Tỷ lệ nhân sự"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<1%"}}, {"value": "b", "text": {"vi": "1–5%"}}, {"value": "c", "text": {"vi": "5–15%"}}, {"value": "d", "text": {"vi": "5–15%"}}]}, {"type": "radiogroup", "name": "question29", "title": {"vi": "Nhân sự tại các lĩnh vực chuyên môn của doanh nghiệp đã đư<PERSON><PERSON> đào tạo về AI hay chưa"}, "description": {"vi": "Chuyên gia hiểu biết về AI trong các lĩnh vự<PERSON> chuyên môn - <PERSON><PERSON><PERSON> tạo cho lực lượng chuyên môn về AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Chưa đào tạo"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> đào tạo nhỏ lẻ, không đồng đều"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> tạo có kế hoạch đ<PERSON><PERSON> kỳ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> tạo có kế hoạch đ<PERSON><PERSON> kỳ"}}]}, {"type": "radiogroup", "name": "question30", "title": {"vi": "Tỷ lệ nhân sự chuyên môn có thể sử dụng AI vào công việc một cách bài bản"}, "description": {"vi": "Chuyên gia hiểu biết về AI trong các lĩnh vực chuyên môn - Tỷ lệ người sử dụng AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> <PERSON> kh<PERSON>ng"}}, {"value": "b", "text": {"vi": "1–5%"}}, {"value": "c", "text": {"vi": "5–20%"}}, {"value": "d", "text": {"vi": "5–20%"}}]}, {"type": "radiogroup", "name": "question31", "title": {"vi": "<PERSON><PERSON><PERSON> lãnh đạo cấp trung trở lên đã được tập huấn và đánh giá kỹ năng số, kh<PERSON> năng sáng tạo hay chưa?"}, "description": {"vi": "K<PERSON> năng lãnh đạo - <PERSON><PERSON>c lãnh đạo có kỹ năng mới về quản trị sáng tạo, tư duy về dữ liệu, kỹ năng số và tổ chức triển khai dự án số"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Chưa"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> đ<PERSON>h giá thử nghiệm"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> ch<PERSON>ng trình phát triển kỹ năng số"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> ch<PERSON>ng trình phát triển kỹ năng số"}}]}, {"type": "radiogroup", "name": "question32", "title": {"vi": "<PERSON>hân viên các bộ phận đã có nhận thức cơ bản về AI hay chưa?"}, "description": {"vi": "<PERSON><PERSON> năng của lực lượng lao động - Tình trạng nhận thức của nhân sự doanh nghiệp về AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có chương trình"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> tập hu<PERSON>n c<PERSON> bản, ng<PERSON><PERSON> quãng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> thức AI phổ biến trong toàn tổ chức"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> thức AI phổ biến trong toàn tổ chức"}}]}, {"type": "radiogroup", "name": "question33", "title": {"vi": "<PERSON><PERSON>h nghiệp đã tổ chức đào tạo/cập nhật kiến thức nền tảng về AI cho toàn doanh nghiệp chưa?"}, "description": {"vi": "<PERSON><PERSON> năng của lực lượng lao động - <PERSON><PERSON><PERSON><PERSON> đào tạo về AI trong doanh nghiệp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>n"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> buổi gi<PERSON>i thi<PERSON>u, h<PERSON><PERSON> thảo"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON>ng trình đào tạo định kỳ/quý"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON>ng trình đào tạo định kỳ/quý"}}]}, {"type": "radiogroup", "name": "question34", "title": {"vi": "Nhân sự công ty có mức độ tin tưởng và sẵn sàng sử dụng các hệ thống có AI như thế nào?"}, "description": {"vi": "<PERSON><PERSON> năng của lực lượng lao động - <PERSON><PERSON><PERSON> năng đón nhận AI trong doanh nghiệp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Lo ngại hoặc không tin tưởng"}}, {"value": "b", "text": {"vi": "Tin tưởng mức trung bình"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> dụng có l<PERSON> chọn, tin tưởng công nghệ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> dụng có l<PERSON> chọn, tin tưởng công nghệ"}}]}, {"type": "radiogroup", "name": "question35", "title": {"vi": "<PERSON><PERSON> chương trình phát triển kỹ năng  số, kỹ năng liên quan đến dữ liệu phổ cập cho các cấp nhân viên không?"}, "description": {"vi": "<PERSON><PERSON> năng của lực lượng lao động - <PERSON><PERSON> năng số toàn doanh nghiệp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có"}}, {"value": "b", "text": {"vi": "Chỉ dành cho bộ phận kỹ thuật"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> học nội bộ theo từng cấp bậc"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> học nội bộ theo từng cấp bậc"}}]}, {"type": "radiogroup", "name": "question36", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có ưu tiên vi<PERSON><PERSON> lậ<PERSON> luậ<PERSON>, đ<PERSON> xuất và ra quyết định dựa trên dữ liệu hay không"}, "description": {"vi": "<PERSON><PERSON><PERSON> hóa dữ liệu- <PERSON><PERSON><PERSON><PERSON> định dựa trên dữ liệu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> y<PERSON> c<PERSON>, <PERSON><PERSON> nghi<PERSON>m"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> tham kh<PERSON>o dữ liệu khi cần thiết"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> định thường xuyên dựa dữ liệu"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> quyết định đều dựa dữ liệu, dữ liệu là tài sản chiến lư<PERSON>"}}]}, {"type": "radiogroup", "name": "question37", "title": {"vi": "Nhân viên có chủ động đặt câu hỏi, khai phá dữ liệu để tìm ra insight mới không?"}, "description": {"vi": "<PERSON><PERSON>n hóa dữ liệu- Thói quen sử dụng dữ liệu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON>, chờ yêu cầu"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> một số nhóm chủ động"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> bộ phận khai phá dữ liệu thườ<PERSON> xuyên"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> khích to<PERSON><PERSON>, có quy trình khen thưởng cho insight mới"}}]}, {"type": "radiogroup", "name": "question38", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có khu<PERSON>ến khích nhân viên đề xuất ý tưởng mới, đặc biệt liên quan đến công nghệ và AI không?"}, "description": {"vi": "<PERSON><PERSON> duy đổi mới - <PERSON><PERSON> chế khuyến khích đổi mới"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Không hoặc rất ít khuyến khích"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> k<PERSON>n khích không ch<PERSON>h thức"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> ch<PERSON>ng trình nội bộ gợi mở sáng kiến"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> v<PERSON><PERSON><PERSON> ư<PERSON><PERSON> đổi mới, <PERSON><PERSON><PERSON> thi AI, sandbox thử nghiệm"}}]}, {"type": "radiogroup", "name": "question39", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có chấp nhận tư duy fast-fail và không tạo áp lực cho các ý tưởng mới khi triển khai hay không?"}, "description": {"vi": "<PERSON><PERSON> duy đổi mới - <PERSON><PERSON> duy fast fail"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>i bị quy tr<PERSON>ch n<PERSON>m"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> mức độ chấp nhận thất bại nhỏ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> thử nghiệm đư<PERSON>c chấp nhận & h<PERSON><PERSON> hỏi"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch thử – sai, đ<PERSON><PERSON> mới <PERSON>, kh<PERSON><PERSON> ph<PERSON>t thất bại"}}]}, {"type": "radiogroup", "name": "question40", "title": {"vi": "<PERSON><PERSON><PERSON> nghi<PERSON><PERSON> có quỹ, t<PERSON> chức, ngân sách cho việc R&D và triển khai các ý tưởng mới hay không?"}, "description": {"vi": "<PERSON><PERSON> duy đổi mới - <PERSON><PERSON> chức, ng<PERSON> sách cho đổi mới"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> quỹ nhỏ, kh<PERSON>ng ổn định"}}, {"value": "c", "text": {"vi": "Ngân sách R&D rõ ràng theo năm"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> đ<PERSON>n vị R&D chuyên trách, ngân sách chiến l<PERSON>"}}]}, {"type": "radiogroup", "name": "question41", "title": {"vi": "Trong 2–3 n<PERSON><PERSON> g<PERSON><PERSON>, <PERSON><PERSON><PERSON> nghi<PERSON>p đã từng ứng dụng công nghệ mới nào vào quy trình chưa?"}, "description": {"vi": "Tư duy đổi mới - Ứng dụng, triển khai đổi mới vào thực tiễn"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có thử nghiệm công nghệ mới"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> <PERSON>ng dụng nhưng chưa rõ kết quả"}}, {"value": "c", "text": {"vi": "Đã áp dụng rõ ràng 1–2 công nghệ mới"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> dụng nhiều công ngh<PERSON> mới, đổi mới liên tục quy trình"}}]}, {"type": "radiogroup", "name": "question42", "title": {"vi": "<PERSON><PERSON> có công nghệ mới (như GenAI), do<PERSON><PERSON> nghiệp có chủ động tìm hiểu và thử nghiệm hay chờ đến khi thị trường chín muồi?"}, "description": {"vi": "<PERSON><PERSON> duy đổi mới - <PERSON><PERSON>h chủ động trong nghiên cứu <PERSON>ng dụng <PERSON>"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON> thị trường chín muồi mới triển khai"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> s<PERSON>t xu h<PERSON>, c<PERSON> <PERSON> thử"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> động thử nghiệm khi công nghệ mới ra đời"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> cực ng<PERSON>, thí đi<PERSON>m sớm, có quy trình tiếp nhận công nghệ mới"}}]}]}, {"name": "page4", "title": {"vi": "<PERSON><PERSON><PERSON>n trị và vận hành"}, "elements": [{"type": "radiogroup", "name": "question43", "title": {"vi": "<PERSON><PERSON><PERSON> <PERSON><PERSON> dụng AI có quy trình bài bản để mở rộng hay không"}, "description": {"vi": "Quản trị vận hành và triển khai - Việc mở rộng quy mô và duy trì các giải pháp và quy trình AI một cách đáng tin cậy và hiệu quả trong môi trường sản xuất"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> thử đơn lẻ, kh<PERSON>ng có quy trình"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> kế hoạch mở rộng nhưng chưa chuẩn hóa"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> quy trình mở rộng bài bản"}}, {"value": "d", "text": {"vi": "Mở rộng AI theo mô hình CI/CD (tích hợp lập trình và đưa vào ứng dụng liên tục), quy mô hóa hiệu quả"}}]}, {"type": "radiogroup", "name": "question44", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp thiết lập và áp dụng các tiêu chuẩn và giải pháp AI dựa trên xu hướng ngành hay không?"}, "description": {"vi": "Quản trị vận hành và triển khai - <PERSON><PERSON><PERSON> nghiệp thiết lập và áp dụng các tiêu chuẩn và giải pháp AI dựa trên xu hướng ngành"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> theo chuẩn nào cụ thể"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> tham kh<PERSON>o nhưng chưa áp dụng rõ ràng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> dụng một số tiêu chuẩn"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> dụng một số tiêu chuẩn"}}]}, {"type": "radiogroup", "name": "question45", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã có nhận diện các rủi ro liên quan đến ứng dụng AI tại doanh nghiệp hay chưa?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý rủi ro - <PERSON><PERSON><PERSON>n diện rủi ro AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> <PERSON>"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>n sơ bộ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> danh s<PERSON>ch rủ<PERSON> ro, phân tích đ<PERSON>nh l<PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> ro <PERSON> đ<PERSON> đưa vào quản trị rủi ro tổ chức"}}]}, {"type": "radiogroup", "name": "question46", "title": {"vi": "<PERSON><PERSON><PERSON> giá rủi ro tổng thể của doanh nghiệp đã bao hàm cả các rủi ro liên quan đến AI?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý rủi ro - R<PERSON><PERSON> ro <PERSON> đư<PERSON><PERSON> đ<PERSON>h giá cùng với tổng thể đánh giá rủi ro doanh nghiệp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> đề cập AI"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> cập nhưng lồng gh<PERSON>p"}}, {"value": "c", "text": {"vi": "<PERSON> đ<PERSON><PERSON><PERSON> đ<PERSON>h giá trong khung rủi ro tổng thể"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> ma trận rủi ro chuyên bi<PERSON>t cho AI"}}]}, {"type": "radiogroup", "name": "question47", "title": {"vi": "<PERSON><PERSON><PERSON> giá rủi ro trước khi triển khai AI có được thực hiện hay không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý rủi ro - <PERSON><PERSON><PERSON> giá rủi ro trư<PERSON><PERSON> khi triển khai AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có b<PERSON><PERSON>c đ<PERSON>h giá"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON><PERSON> giá đơn giản"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> đ<PERSON>h giá tác động trước triển khai"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> giá rủi ro đa chiều trướ<PERSON>, trong, sau triển khai"}}]}, {"type": "radiogroup", "name": "question48", "title": {"vi": "Có quy trình để xử lý rủi ro khi xảy ra hay chưa?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý rủi ro - Quy trình xử lý rủi ro AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> c<PERSON>"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> lý tình h<PERSON>, <PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> bà<PERSON> bản"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> quy trình xử lý rõ ràng, đ<PERSON><PERSON> kỳ cập nh<PERSON>t"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> c<PERSON> chế ph<PERSON>n hồ<PERSON>, t<PERSON><PERSON> hu<PERSON>n luy<PERSON>n"}}]}, {"type": "radiogroup", "name": "question49", "title": {"vi": "<PERSON><PERSON><PERSON> bộ phận liên quan (ph<PERSON><PERSON> chế, kiể<PERSON> to<PERSON> nội bộ, quản lý rủi ro) có được tham gia vào đánh giá các hệ thống AI không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý rủi ro - <PERSON><PERSON> công đánh giá rủi ro AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>ng tham gia"}}, {"value": "b", "text": {"vi": "Tham gia sau khi triển khai"}}, {"value": "c", "text": {"vi": "Tham gia từ đầu giai đoạn thiết kế hệ thống"}}, {"value": "d", "text": {"vi": "Gắn chặt trong mọi giai đoạn vòng đời của AI"}}]}, {"type": "radiogroup", "name": "question50", "title": {"vi": "Người dùng cuối (internal hoặc external) có được thông báo khi họ tương tác với hệ thống AI không?"}, "description": {"vi": "AI đ<PERSON>o đ<PERSON> vầ có trách nhi<PERSON> - <PERSON><PERSON><PERSON><PERSON> báo cho người dùng về việc sử dụng AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> thông báo"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> một số thông báo mờ nhạt"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> thông báo rõ ràng tại điểm tương tác"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> ch<PERSON>h s<PERSON>ch minh bạch AI (AI transparency policy)"}}]}, {"type": "radiogroup", "name": "question51", "title": {"vi": "<PERSON><PERSON><PERSON> quả của AI có thể bị kiểm tra, truy vết nguồn dữ liệu và logic đưa ra quyết định không?"}, "description": {"vi": "AI đ<PERSON><PERSON> đ<PERSON><PERSON> vầ có trách nhiệm - <PERSON><PERSON> cứu t<PERSON>h xác thực trong kết quả của AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có khả năng truy vết"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> log nhưng chưa đầy đủ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> h<PERSON> thống <PERSON> chi tiết"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> ghi nhận log đầy đủ và giải thích nguyên nhân sự kiện"}}]}, {"type": "radiogroup", "name": "question52", "title": {"vi": "AI của doanh nghiệp có sử dụng thông tin cá nhân nhạy cảm không? <PERSON><PERSON><PERSON> có, có tuân thủ các quy định pháp lý như GDPR, PDP không?"}, "description": {"vi": "AI đạo đứ<PERSON> vầ có trách nhiệm - <PERSON><PERSON> thủ các quy định pháp lý liên quan tới AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON>, nhưng không kiểm soát rõ"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> ch<PERSON>h s<PERSON>ch nhưng chưa triệt để"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> tuân thủ quy định nội bộ & một số chuẩn"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> bảo tuân thủ chặt chẽ GDPR, PDP, cross-border data rules"}}]}, {"type": "radiogroup", "name": "question53", "title": {"vi": "<PERSON><PERSON> ch<PERSON>h sách rõ ràng về lưu trữ, t<PERSON><PERSON> cập và sử dụng dữ liệu huấn luyện?"}, "description": {"vi": "AI đ<PERSON><PERSON> đ<PERSON> vầ có trách nhiệm - <PERSON><PERSON><PERSON> s<PERSON>ch trong triển khai <PERSON>"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Không rõ ràng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> quy định nội bộ sơ bộ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> ch<PERSON>h s<PERSON>ch rõ ràng, ph<PERSON> quyền sử dụng"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> s<PERSON>ch to<PERSON>, gắn v<PERSON><PERSON> b<PERSON><PERSON> mậ<PERSON>, quyề<PERSON> riêng tư"}}]}, {"type": "radiogroup", "name": "question54", "title": {"vi": "<PERSON><PERSON> xác định rõ ai chịu trách nhiệm cuối cùng cho quyết định do AI đưa ra không (AI vs con ngườ<PERSON>)?"}, "description": {"vi": "AI đ<PERSON><PERSON> đứ<PERSON> vầ có trách nhiệm - <PERSON> bạch trách nhiệm trong sử dụng AI để ra quyết định"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> rõ ràng (AI \"tự vận hành\")"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> tr<PERSON>ch nhiệm chung nhưng không gán cụ thể"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> phân công ngư<PERSON>i chịu trách nhiệm nghiệp vụ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> ch<PERSON>h sách phân rõ vai trò giữa AI & con người"}}]}, {"type": "radiogroup", "name": "question55", "title": {"vi": "<PERSON><PERSON> thực hiện đánh giá tác động xã hội hoặc môi trường trước khi triển khai giải pháp AI quy mô lớn?"}, "description": {"vi": "AI đạo đứ<PERSON> vầ có trách nhiệ<PERSON> - <PERSON><PERSON><PERSON> gi<PERSON> tác động xã hội"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> xem xét không ch<PERSON>h thức"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON>h giá sơ bộ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> ESG, AI Ethics trước mỗi dự án quy mô lớn"}}]}, {"type": "radiogroup", "name": "question56", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có phân tích tác động pháp lý hoặc tuân thủ trước khi triển khai các giải pháp AI không?"}, "description": {"vi": "<PERSON><PERSON>n hóa tuân thủ - <PERSON><PERSON><PERSON> giá tuân thủ"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>ng có phân tích"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> xem xét nội bộ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> phân tích & báo cáo tuân thủ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> khung pháp lý đồng hành trước mọi triển khai"}}]}, {"type": "radiogroup", "name": "question57", "title": {"vi": "<PERSON><PERSON> kiểm tra định kỳ việc tuân thủ các quy định về bảo vệ dữ liệu cá nhân, phân bi<PERSON>t đ<PERSON><PERSON> xử, minh bạch... khi triển khai AI không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> hóa tuân thủ - <PERSON><PERSON><PERSON> tra tuân thủ định kỳ"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> kiểm tra"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> kiểm tra sau khi phát sinh rủi ro"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> tra đ<PERSON><PERSON> k<PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> tra liên tụ<PERSON>, c<PERSON> đội kiểm tra tuân thủ độc lập"}}]}, {"type": "radiogroup", "name": "question58", "title": {"vi": "<PERSON><PERSON><PERSON> yêu cầu thay đổi về chuyên môn nghiệp vụ có được chuyển hóa thành các thay đổi trong công nghệ hay không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý sự thay đổi - <PERSON><PERSON> chức dịch các yêu cầu nghiệp vụ thành các thay đổi công nghệ có sự tham gia của AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có quy trình liên kết"}}, {"value": "b", "text": {"vi": "<PERSON>hay đ<PERSON>i thủ công, chậ<PERSON> tr<PERSON>"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> luồng trao đổi 2 chiều giữa nghiệp vụ và kỹ thuật"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> c<PERSON> chế agile, l<PERSON>h ho<PERSON>t hóa quy trình theo thay đổi nghiệp vụ"}}]}, {"type": "radiogroup", "name": "question59", "title": {"vi": "Tất cả các thay đổi trong kinh doanh vận hành nằm trong dự tính và hợp với nhu cầu quản lý chiến lược hay không"}, "description": {"vi": "Qu<PERSON>n lý sự thay đổi - T<PERSON><PERSON> cả các thay đổi trong kinh doanh vận hành nằm trong dự tính và hợp với nhu cầu quản lý chiến lược"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON>hay đ<PERSON>i phát sinh rời rạc"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> kế hoạch tổng thể nhưng cập nhật chậm"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON>n thay đổi với roadmap công nghệ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON>n thay đổi vào quản trị chiến lư<PERSON><PERSON> do<PERSON>h nghi<PERSON>p"}}]}, {"type": "radiogroup", "name": "question60", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có ghi nhận quá trình và các phiên bản thay đổi về công nghệ hay không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý sự thay đổi - <PERSON><PERSON><PERSON><PERSON> lý các phiên bản thay đổi"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> ghi nhận ch<PERSON>h thức"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> ghi nhận qua email/tài liệu rời rạc"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> hệ thống quản lý thay đổi (Change log)"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> hệ thống quản lý phiên bản, đ<PERSON><PERSON> gi<PERSON> hậu thay đổi"}}]}, {"type": "radiogroup", "name": "question61", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có các chỉ số đánh giá mức độ thích nghi, cam kết và hiệu quả thay đổi không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý sự thay đổi - <PERSON><PERSON><PERSON> giá sự thay đổi"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có chỉ số"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> m<PERSON>t vài KPI chung"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> bộ KPI/BSC cho từng giai đoạn"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> hệ thống dashboard, feedback loop theo thời gian thực"}}]}, {"type": "radiogroup", "name": "question62", "title": {"vi": "Có quy trình phản hồi và điều chỉnh nếu thay đổi ban đầu gây xung đột hoặc không đạt hiệu quả không?"}, "description": {"vi": "<PERSON><PERSON><PERSON><PERSON> lý sự thay đổi - <PERSON><PERSON><PERSON> <PERSON><PERSON> lại với các tác động tiêu cực của thay đổi"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON>ng thủ công, không có quy trình"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> cơ chế tạm thời xử lý mâu thuẫn"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> quy trình escalation, phân xử"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> vòng p<PERSON><PERSON><PERSON> h<PERSON>, mô hình học thích nghi tổ chức (adaptive org)"}}]}]}, {"name": "page5", "title": {"vi": "<PERSON>ến <PERSON><PERSON><PERSON> và Lãnh đạo"}, "elements": [{"type": "radiogroup", "name": "question63", "title": {"vi": "<PERSON><PERSON><PERSON> hướng về thị trư<PERSON><PERSON>, <PERSON>h<PERSON><PERSON> hàng có xác định sử dụng các quy trình và công cụ để đạt đư<PERSON>c thông tin  hiểu biết về thị trường, đối thủ cạnh tranh và khách hàng hay không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON>h do<PERSON>h - <PERSON><PERSON><PERSON> hướng thị trườ<PERSON>, <PERSON><PERSON><PERSON><PERSON> hàng có nội hàm sử dụng công nghệ, dữ liệu để nắm bắ<PERSON> thị tr<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, từ đó đưa ra định hướng chiế<PERSON> lư<PERSON><PERSON> phù hợp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có hoặc rất ít quy trình thu thập và phân tích dữ liệu thị trường/khách hàng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> công cụ hoặc đội ngũ phân tích, nhưng vận hành còn rời rạc"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> liệu thị trường và khách hàng đư<PERSON><PERSON> tích hợp vào quy trình ra quyết định"}}, {"value": "d", "text": {"vi": "<PERSON>uy trình phân tích thị trường/khách hàng có tích hợp AI/ML để dự báo, phân khúc nâng cao"}}]}, {"type": "radiogroup", "name": "question64", "title": {"vi": "<PERSON><PERSON><PERSON> cơ hội tăng trưởng được xác định và đánh giá trong điều kiện có xem xét công nghệ, quy tr<PERSON><PERSON>, dữ liệu là yếu tố cạnh tranh cần thiết"}, "description": {"vi": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> kinh doanh - <PERSON><PERSON> trò của công nghệ, dữ liệu trong năng lực cạnh tranh của doanh nghiệp"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Tăng trưởng được xem là mở rộng truyền thống, ch<PERSON><PERSON> quan tâm yếu tố dữ liệu/công nghệ"}}, {"value": "b", "text": {"vi": "Một số sáng kiến tăng trưởng có nhắc đến công nghệ, nh<PERSON><PERSON> chưa hệ thống"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> liệu và công nghệ nằm trong chiến lược tăng trưởng trung hạn"}}, {"value": "d", "text": {"vi": "<PERSON>, dữ liệu và tự động hóa là hạt nhân cho đổi mới mô hình kinh doanh"}}]}, {"type": "radiogroup", "name": "question65", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có coi tiếp thị số là một hình thức quan trọng và hiệu quả trong việc tiếp cận và thúc đẩy doanh thu?"}, "description": {"vi": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> kinh doanh - <PERSON><PERSON><PERSON> hoạt động tiếp thị số được vận hành để cho phép khách hàng tiếp cận và tăng doanh thu"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> thị truyền thống là chủ đạo, digital chỉ là phụ trợ"}}, {"value": "b", "text": {"vi": "Bắt đầu triển khai digital marketing nhưng chưa tận dụng dữ liệu người dùng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> chiến dịch digital marketing cá nhân hóa theo phân tích hành vi"}}, {"value": "d", "text": {"vi": "Digital marketing sử dụng AI (recommendation, automation, targeting nâng cao) để tối ưu trải nghiệ<PERSON> & chuyển đổi"}}]}, {"type": "radiogroup", "name": "question66", "title": {"vi": "<PERSON><PERSON><PERSON>, mục tiêu chiến lư<PERSON><PERSON> có bao hàm mục tiêu trong nghiên cứu, <PERSON><PERSON>, tri<PERSON><PERSON> khai <PERSON> không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON>h do<PERSON>h - <PERSON><PERSON><PERSON>, mục tiêu chiế<PERSON> lư<PERSON><PERSON> có bao hàm mục tiêu trong nghiê<PERSON> c<PERSON>, <PERSON><PERSON>, tri<PERSON><PERSON> khai <PERSON>"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> c<PERSON> định hướng <PERSON>ng dụng <PERSON>"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> <PERSON><PERSON> đ<PERSON> AI như một mục tiêu tiềm năng"}}, {"value": "c", "text": {"vi": "<PERSON> đ<PERSON><PERSON><PERSON> tích hợp trong một số mục tiêu chiến l<PERSON><PERSON><PERSON> cụ thể"}}, {"value": "d", "text": {"vi": "AI là trụ cột chiến lư<PERSON><PERSON> dài hạn, có roadmap rõ ràng từ thí điểm đến mở rộng"}}]}, {"type": "radiogroup", "name": "question67", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã xác định và có định hướng bài bản về các trường hợp sử dụng (use case) trong kinh doanh, vận hành mà có khả năng cải thiện nhờ công nghệ thông minh và AI?"}, "description": {"vi": "<PERSON><PERSON><PERSON> định các trường hợp sử dụng cho triển khai AI - <PERSON><PERSON><PERSON> định và đưa vào định hướng dài hạn việc cải thiện các điểm nghẽn lớn cần cải thiện hoặc nhu cầu trọng yếu cần đạt bằng công nghệ"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Chưa có danh sách use case cụ thể; chưa đ<PERSON>h giá <PERSON> có thể ứng dụng vào đâu"}}, {"value": "b", "text": {"vi": "Một vài nhóm đơn lẻ có đề xuất use case, nhưng chưa được tổng hợp hoặc ưu tiên"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> sách use case đã được đánh giá về tính khả thi và tác động; có một số đang thử nghiệm"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> chiến lư<PERSON><PERSON> ph<PERSON> triển use case AI toàn do<PERSON>h nghi<PERSON>, <PERSON><PERSON> tiên theo tác động kinh doanh và khả năng triển khai, có roadmap cụ thể"}}]}, {"type": "radiogroup", "name": "question68", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã xác định và có định hướng bài bản về các trường hợp sử dụng (use case) trong kinh do<PERSON>h, vận hành mà có nhu cầu sử dụng công nghệ hiện đại, AI để tăng năng suất và hỗ trợ khi khối lượng tác nghiệp lớn"}, "description": {"vi": "<PERSON><PERSON><PERSON> định các trường hợp sử dụng cho triển khai AI - <PERSON><PERSON><PERSON> định và đưa vào định hướng dài hạn việc cần sử dụng công nghệ thông minh để thực hiện các hoạt động có quy trình, sử dụng dữ liệu lớn"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> nhận diện rõ quy trình nào có thể tăng hiệu quả nhờ AI"}}, {"value": "b", "text": {"vi": "Một số quy trình khối tác nghiệp hoặc vận hành có đề xuất áp dụng AI nhưng chưa rõ ràng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> đ<PERSON>h giá cụ thể về quy trình có tiềm năng tự động hóa bằng AI (ví dụ: xử lý đơn hàng, ch<PERSON><PERSON><PERSON><PERSON>, RPA...)"}}, {"value": "d", "text": {"vi": "Các use case tăng năng suất bằng AI được tích hợp vào kế hoạch chuyển đổi số, có ROI ước lượ<PERSON>, có gi<PERSON>i pháp kỹ thuật đề xuất sẵn"}}]}, {"type": "radiogroup", "name": "question69", "title": {"vi": "<PERSON><PERSON><PERSON> đạo cấp cao đã tìm hiểu cơ bản về AI và có ý định rõ ràng về vai trò của AI trong doanh nghiệp mình hay chưa?"}, "description": {"vi": "<PERSON> kết từ lãnh đạo - Sự am hiểu về AI và nhận biết ý nghĩa, vai trò của AI với doanh nghiệp một cách rõ ràng"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có nhận thức hoặc nhận thức rời rạ<PERSON>, chưa coi AI là yếu tố chiến lược"}}, {"value": "b", "text": {"vi": "Một vài lãnh đạo đã bắt đầu tìm hiểu và quan tâm tới AI"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> đạo hiểu rõ tiềm năng của AI và đề cập trong các định hướng chiến lư<PERSON>c"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON>nh đạo coi AI là một phần cốt lõi trong tầm nhìn dài hạn, c<PERSON> định hướng rõ ràng về ứng dụng AI toàn do<PERSON>h nghiệp"}}]}, {"type": "radiogroup", "name": "question70", "title": {"vi": "<PERSON><PERSON><PERSON> đạo cấp cao có chỉ đạo rõ ràng và thường xuyên thúc đ<PERSON>y, gi<PERSON><PERSON> sát việc triển khai nghiên cứu ứng dụng AI trong doanh nghiệp?"}, "description": {"vi": "<PERSON> kết từ lãnh đạo - <PERSON><PERSON><PERSON> độ tích cực và kiên định trong thúc đẩy nghiên cứ<PERSON>, <PERSON><PERSON> d<PERSON>, triể<PERSON> khai <PERSON> của Lãnh đạo"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có chỉ đạo cụ thể nào từ cấp cao về AI"}}, {"value": "b", "text": {"vi": "Chỉ đạo ở mức khu<PERSON> kh<PERSON>, chư<PERSON> tạo thành ưu tiên chiến lược"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> chỉ đạo rõ r<PERSON>, có gi<PERSON>m sát tiến độ nghiên cứu ứng dụng AI"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> đạo cấp cao trực tiếp dẫn dắt, bảo trợ và đánh giá hiệu quả các chương trình AI"}}]}, {"type": "radiogroup", "name": "question71", "title": {"vi": "Trong Ban lãnh đạo doanh nghiệp có sự thống nhất về nhu cầu, định hướng triển khai AI?"}, "description": {"vi": "<PERSON> kết từ lãnh đạo - Sự nhất quán trong ban lãnh đạo về triển khai AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Mỗi bộ phận có quan điểm kh<PERSON>u, thi<PERSON><PERSON> sự phối hợp"}}, {"value": "b", "text": {"vi": "Một số lãnh đạo <PERSON>, nh<PERSON><PERSON> chưa có tiếng nói chung"}}, {"value": "c", "text": {"vi": "Ban lãnh đạo đã có sự đồng thuận về định hướng, tuy chưa đư<PERSON><PERSON> thể chế hóa đầy đủ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> sự thống <PERSON>h<PERSON> cao, <PERSON> <PERSON><PERSON><PERSON><PERSON> lồng gh<PERSON>p vào các quyết sách chiến lư<PERSON>c liên phòng ban"}}]}, {"type": "radiogroup", "name": "question72", "title": {"vi": "Cấp C-level của doanh nghiệp đã có phân công rõ ràng một lãnh đạo nào về triển khai AI hay chưa"}, "description": {"vi": "<PERSON> kết từ lãnh đạo - <PERSON><PERSON><PERSON> hướng về bố trí nhân sự cấp cao liên quan đến triển khai AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> c<PERSON> ng<PERSON><PERSON><PERSON> ph<PERSON> tr<PERSON>, tr<PERSON><PERSON> AI không rõ ràng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> người tạm thời đảm nhiệm, nh<PERSON><PERSON> không rõ vai trò ch<PERSON>h thức"}}, {"value": "c", "text": {"vi": "Đã chỉ định người chịu trách nhi<PERSON> ch<PERSON>, nh<PERSON><PERSON> còn thiếu quyền hạn & nguồn lực đầy đủ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> vị trí ch<PERSON> (CDO, CAIO hoặc tương đương) đượ<PERSON> trao quyền và nguồn lực để dẫn dắt triển khai AI toàn doanh nghiệp"}}]}, {"type": "radiogroup", "name": "question73", "title": {"vi": "<PERSON><PERSON><PERSON> nghi<PERSON><PERSON> có bố trí ngân sách cho hoạt động nghi<PERSON> c<PERSON>, <PERSON><PERSON> d<PERSON><PERSON> ch<PERSON>?"}, "description": {"vi": "<PERSON><PERSON><PERSON> sách đầu tư - Tổ chức có một lượng vốn đầu tư thỏa đáng để triển khai AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có ngân sách riêng, hoặc chỉ có mức ngân sách rất nhỏ, kh<PERSON><PERSON> <PERSON>n định"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> ngân sách dự phòng cho AI, nhưng chưa phải là một hạng mục ngân sách thường niên"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> ngân sách định kỳ cho nghiên c<PERSON>/<PERSON><PERSON> dụng <PERSON>, nằm trong kế hoạch số hóa/đổ<PERSON> mới"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> sách AI là một phần trong chiến lư<PERSON><PERSON> đầu tư trung/dài hạn, đ<PERSON><PERSON><PERSON> điều chỉnh linh hoạt theo nhu cầu triển khai"}}]}, {"type": "radiogroup", "name": "question74", "title": {"vi": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> ngân, qu<PERSON><PERSON> lý sử dụng ngân sách cho AI có khả năng bị cản trở hay không?"}, "description": {"vi": "<PERSON><PERSON><PERSON> sách đầu tư - <PERSON><PERSON><PERSON> đầu tư đư<PERSON><PERSON> quản lý, có quy trình để phê du<PERSON>, g<PERSON><PERSON><PERSON> ngân phù hợp cho triển khai AI"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> ngân k<PERSON>, thủ tụ<PERSON> chậm tr<PERSON>, thi<PERSON><PERSON> l<PERSON>h ho<PERSON>t"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> ngân được nhưng thường xuyên vướng rào cản thủ tục hoặc hạn chế nguồn lực"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> chế giải ngân đ<PERSON><PERSON><PERSON> cả<PERSON> thi<PERSON>, c<PERSON> kiểm soát và linh hoạt theo giai đoạn triển khai"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> cơ chế quản lý ngân sách AI hi<PERSON>u quả, p<PERSON><PERSON><PERSON> <PERSON><PERSON>h với thay đổi, gắn liền với các mô hình quản trị đổi mới"}}]}, {"type": "radiogroup", "name": "question75", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có sẵn sàng triển khai các mô hình đầu tư mới (hợ<PERSON> tác đầu tư, vườn ươm, sand-box, thuê mua công nghệ…) cho các dự án AI?"}, "description": {"vi": "<PERSON><PERSON><PERSON> sách đầu tư - Tổ chức đang sử dụng các mô hình đầu tư và tài chính mới để đẩy nhanh triển khai <PERSON>"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> từng thử các mô hình đầu tư mới cho công ngh<PERSON>, AI"}}, {"value": "b", "text": {"vi": "<PERSON>ã có cân <PERSON>, thử nghiệm mô hình hợp tác hoặc thuê ngoài nhỏ lẻ"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> nghiệp có áp dụng một số mô hình linh hoạt như thuê nền tảng <PERSON>, h<PERSON><PERSON> tác chiến l<PERSON>"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON><PERSON> nghiệp chủ động tìm kiế<PERSON>, triển khai nhiều mô hình đầu tư mở: sandbox, đổi mới mở, li<PERSON><PERSON><PERSON>, mua cổ phần công nghệ..."}}]}, {"type": "radiogroup", "name": "question76", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã xác định rõ các bên liên quan có thể có trong quá trình triển khai AI hay chưa"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - Tổ chức ảnh hưởng và khuyến khích các bên liên quan theo định hướng \"đối tác\", có trách nhiệm và quản lý rủi ro "}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> xác định rõ các đối tác hay bên liên quan tiềm năng"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> bắt đ<PERSON><PERSON> lập danh sách đ<PERSON>i tác, nh<PERSON><PERSON> chưa có phân loại rõ ràng"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON>, ph<PERSON> loại đối tác AI trong chuỗi giá trị"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> sách đối tác đư<PERSON><PERSON> cập nhật thư<PERSON><PERSON>, tích hợp vào chiến lư<PERSON><PERSON> hệ sinh thái"}}]}, {"type": "radiogroup", "name": "question77", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có xây dựng nguyên tắc hợp tác và quản lý đối tác cho việc hợp tác triển khai <PERSON>?"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - <PERSON><PERSON> các nguyên tắc và phương pháp quản lý truyền thông rõ ràng và cởi mở cho các mối quan hệ minh bạch và tin cậy trong hệ sinh thái"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> có ch<PERSON>h s<PERSON>ch cụ thể"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> quy định sơ bộ, ch<PERSON><PERSON> <PERSON>h<PERSON>t quán"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> nguyên tắc hợp tác rõ ràng và áp dụng cho các đối tác hiện tại"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> thống quản trị đối tác đượ<PERSON> chuẩn hóa, kiểm soát chặt chẽ và hỗ trợ phát triển quan hệ dài hạn"}}]}, {"type": "radiogroup", "name": "question78", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có sẵn sàng triển khai nhiều hình thức hợp tác trong triển khai AI"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - <PERSON><PERSON> khả năng tổ chức và quản lý các đối tác thông qua nhiều hình thức (hợ<PERSON> đồng, t<PERSON><PERSON> hợp…)"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Chỉ làm việc với nhà cung cấp truyền thống"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> từng hợp tác dạng dự <PERSON>n, nh<PERSON><PERSON> còn hạn chế hình thức"}}, {"value": "c", "text": {"vi": "Sẵn sàng hợp tác theo nhiều mô hình: thử nghiệm, đồ<PERSON> <PERSON>h<PERSON><PERSON> triển, cùng sở hữu"}}, {"value": "d", "text": {"vi": "<PERSON>a dạng mô hình hợp tác: li<PERSON><PERSON>, vư<PERSON><PERSON> ươ<PERSON>, cộng đồng mở, nguồn mở..."}}]}, {"type": "radiogroup", "name": "question79", "title": {"vi": "Việc sử dụng gi<PERSON><PERSON> pháp AI của bên thứ ba đã có quy trình phù hợp, an toàn từ khâu tiền khả thi, lựa chọn đối tác đến triển khai mua sắm, nghiệm thu thanh toán chưa?"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - Sử dụng giải pháp của bên thứ ba: lự<PERSON> chọn phù hợp và an toàn, có quy trình mua sắm bài bản"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON><PERSON> có quy trình cụ thể, chọn đối tác ngẫu nhiên"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> quy trình lựa chọn nhưng chưa kiểm soát đầy đủ rủi ro"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> tr<PERSON><PERSON> đ<PERSON>, <PERSON><PERSON><PERSON>, ng<PERSON><PERSON><PERSON> thu tương đối đầy đủ và tuân thủ"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> trình <PERSON><PERSON><PERSON> h<PERSON>, t<PERSON><PERSON> hợ<PERSON> rủ<PERSON> ro ph<PERSON><PERSON> l<PERSON>, b<PERSON><PERSON> mật và quản trị chất lư<PERSON>"}}]}, {"type": "radiogroup", "name": "question80", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp đã tìm hiểu các ch<PERSON>h sách hỗ trợ từ cơ quan quản lý và các hi<PERSON><PERSON> hộ<PERSON>, tổ chức ngành nghề để phát triển AI chưa"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - <PERSON><PERSON> quan quản lý, <PERSON><PERSON><PERSON> h<PERSON>, tổ chức: <PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON>, quy đ<PERSON>nh, sứ<PERSON> mạnh tổ chức"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON>a quan tâm hoặc thiếu thông tin"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON> tìm hiểu <PERSON>, k<PERSON><PERSON><PERSON> có kế hoạch cụ thể"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> the<PERSON>, tham gia một số chương trình hỗ trợ"}}, {"value": "d", "text": {"vi": "Chủ động kết nối với các tổ chức quản lý, hiệ<PERSON> hội để tìm kiếm hỗ trợ và chia sẻ chính sách"}}]}, {"type": "radiogroup", "name": "question81", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có kế hoạch hoặc đã thực hiện hợp tác với các trường Đ<PERSON><PERSON> họ<PERSON>, vi<PERSON><PERSON> nghiên cứu để nâng cao kinh nghiệm, thu hút nhân tài cho lĩnh vực AI?"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - <PERSON><PERSON> hệ bên họ<PERSON> thu<PERSON>, nghi<PERSON><PERSON> cứu: nâng cao ki<PERSON> thức, kinh nghiệ<PERSON> bà<PERSON> bản"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "<PERSON><PERSON><PERSON> hợp tác hoặc hợp tác không liên quan đến AI"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON>ợ<PERSON> tác nghiên cứu nhỏ lẻ, ch<PERSON><PERSON> định hướng chiến lược"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> bài bản với viện/trư<PERSON><PERSON> trong nghiê<PERSON> c<PERSON>, thử nghiệm AI"}}, {"value": "d", "text": {"vi": "<PERSON><PERSON> hệ hợp tác chiến lược để chuyển giao công nghệ, đ<PERSON><PERSON> tạ<PERSON> nh<PERSON> lự<PERSON>, ng<PERSON><PERSON><PERSON> c<PERSON><PERSON> dài hạn"}}]}, {"type": "radiogroup", "name": "question82", "title": {"vi": "<PERSON><PERSON><PERSON> nghiệp có định hướng tham gia hệ sinh thái dữ liệu, chia sẻ và tham gia nền tảng nguồn mở và cộng đồng về AI, về dữ liệu hay chưa?"}, "description": {"vi": "<PERSON><PERSON> sinh thái và hợp tác - <PERSON><PERSON> sinh thái dữ liệu: tham gia chia sẻ an toàn, mang lại lợi ích"}, "isRequired": true, "choices": [{"value": "a", "text": {"vi": "Không tham gia cộng đồng mở hoặc chia sẻ dữ liệu"}}, {"value": "b", "text": {"vi": "<PERSON><PERSON><PERSON> b<PERSON>t đầu tìm hiểu, c<PERSON> <PERSON> định tham gia"}}, {"value": "c", "text": {"vi": "<PERSON><PERSON> tham gia một số nền tảng mở, dữ liệu cộng đồng"}}, {"value": "d", "text": {"vi": "Chủ động đóng g<PERSON>, dẫn dắt cộng đồng mở, chia sẻ dữ liệu và kiến thức AI"}}]}]}, {"name": "page6", "title": {"vi": "<PERSON><PERSON><PERSON> qu<PERSON>"}, "elements": [{"type": "expression", "name": "question84", "visible": false, "title": {"vi": "<PERSON><PERSON><PERSON> quả đ<PERSON>h giá"}, "clearIfInvisible": "none", "expression": "iif(({totalScore_CLVLD} > 3.4) and ({totalScore_CN} > 3.6) and ({totalScore_DL} > 3.6) and ({totalScore_CNVHTO} > 3.4) and ({totalScore_QTVVH} > 3.4), '<p><PERSON><PERSON><PERSON> nghiệp đã tích hợp trí tuệ nhân tạo (AI) một cách sâu rộng và có chiến lược vào toàn bộ hoạt động và định hướng phát triển.AI không chỉ là công cụ hỗ trợ, mà đã trở thành yếu tố cốt lõi thúc đẩy đổi mới, hiệu quả vận hành và tạo lợi thế cạnh tranh bền vững.</p><ul><li>🔹 Lãnh đạo cấp cao cam kết mạnh mẽ, chủ động dẫn dắt chiến lược AI gắn với mục tiêu dài hạn.</li><li>🔹 <PERSON><PERSON><PERSON> mô hình <PERSON> được triển khai <PERSON>, quy mô lớn, tích hợ<PERSON> li<PERSON>n mạch vào quy trình kinh doanh cốt lõi.</li><li>🔹 Hạ tầng công nghệ hiện đại, linh hoạt và an toàn, đủ khả năng mở rộng và tối ưu hiệu suất AI.</li><li>🔹 Dữ liệu được quản lý như một tài sản chiến lược, với hệ thống quản trị chất lượng, quyền riêng tư và truy xuất nguồn gốc rõ ràng.</li><li>🔹 Văn hóa tổ chức hỗ trợ đổi mới, học hỏi liên tục, và sẵn sàng thích nghi với những thay đổi do AI mang lại.</li><li>🔹 Doanh nghiệp không chỉ sử dụng AI để tối ưu, mà còn để định hình lại mô hình kinh doanh, khám phá sản phẩm mới, và mở rộng thị trường.</li></ul>', iif(({totalScore_CLVLD} > 2.9) and ({totalScore_CN} > 2.9) and ({totalScore_DL} > 2.9) and ({totalScore_CNVHTO} > 2.4) and ({totalScore_QTVVH} > 2.4), '<p>Doanh nghiệp đã tích hợp AI vào nhiều quy trình kinh doanh và đảm bảo sự liên kết chặt chẽ giữa chiến lược AI và chiến lược tổ chức. AI được xem là công cụ then chốt để cải thiện hiệu suất, nâng cao trải nghiệm khách hàng và hỗ trợ ra quyết định.</p><ul><li>🔹 Chiến lược AI rõ ràng, có định hướng và được cập nhật định kỳ.</li><li>🔹 Các phòng ban đã bắt đầu hợp tác hiệu quả để triển khai AI ở quy mô chức năng hoặc đơn vị.</li><li>🔹 Hạ tầng công nghệ tương đối hiện đại, hỗ trợ triển khai các mô hình AI phức tạp.</li><li>🔹 Đội ngũ nội bộ có kỹ năng phân tích dữ liệu và áp dụng mô hình AI trong công việc hàng ngày.</li><li>🔹 Các vấn đề về đạo đức, bảo mật, và tuân thủ trong triển khai AI được nhận diện và quản lý có hệ thống.</li><li>🔹 Tổ chức bắt đầu có hệ thống đo lường hiệu quả tương đối rõ</li></ul>', iif(({totalScore_CLVLD} > 1.9) and ({totalScore_CN} > 1.9) and ({totalScore_DL} > 1.9) and ({totalScore_CNVHTO} > 1.4) and ({totalScore_QTVVH} > 1.4), '<p>Doanh nghiệp đã có những bước đi bài bản và có cấu trúc trong việc tiếp cận AI, dù ứng dụng thực tế còn giới hạn về quy mô và tác động. Tổ chức đang tập trung xây dựng năng lực nội bộ và xác định các trường hợp sử dụng AI khả thi.</p><ul><li>🔹 Có nhóm chuyên trách dữ liệu hoặc AI, hoạt động tương đối ổn định.</li><li>🔹 Đã triển khai một số dự án AI thử nghiệm (pilot) ở cấp phòng ban hoặc quy trình cụ thể.</li><li>🔹 Hạ tầng công nghệ đang trong quá trình nâng cấp, có khả năng xử lý dữ liệu và hỗ trợ học máy cơ bản.</li><li>🔹 Có quy trình thu thập và quản lý dữ liệu, nhưng chất lượng và mức độ chuẩn hóa còn chưa đồng đều.</li><li>🔹 Nhận thức nội bộ về AI đang được nâng cao thông qua đào tạo, hội thảo và các chương trình chuyển giao kiến thức.</li><li>🔹 Chưa có chiến lược AI rõ ràng ở cấp tổ chức, nhưng có định hướng phát triển</li></ul>', '<p>Doanh nghiệp đang ở giai đoạn khởi đầu trong hành trình AI. Nhận thức về tiềm năng của AI đã xuất hiện, nhưng chưa có sự chuẩn bị hệ thống về công nghệ, con người hoặc chiến lược.</p><ul><li>🔹 Chưa có chiến lược AI chính thức.</li><li>🔹 Việc ứng dụng AI chủ yếu mang tính thử nghiệm, nhỏ lẻ hoặc do bên ngoài cung cấp.</li><li>🔹 Thiếu hụt về kỹ năng dữ liệu và AI trong nội bộ.</li><li>🔹 Cơ sở dữ liệu phân tán, chất lượng chưa được kiểm soát tốt, hạ tầng công nghệ chưa đáp ứng nhu cầu AI.</li><li>🔹 Lãnh đạo nhận thức được vai trò của AI, nhưng chưa cam kết đầu tư mạnh mẽ hoặc dài hạn.</li><li>🔹 Tổ chức chưa xác định rõ giá trị mà AI có thể mang lại.</li></ul>')))"}]}], "calculatedValues": [{"name": "question1Score", "expression": "iif({question1} allof [a], 1, 0) + iif({question1} allof [b], 2, 0) + iif({question1} allof [c], 3, 0) + iif({question1} allof [d], 4, 0)"}, {"name": "question7Score", "expression": "iif({question7} allof [a], 1, 0) + iif({question7} allof [b], 2, 0) + iif({question7} allof [c], 3, 0) + iif({question7} allof [d], 4, 0)"}, {"name": "question6Score", "expression": "iif({question6} allof [a], 1, 0) + iif({question6} allof [b], 2, 0) + iif({question6} allof [c], 3, 0) + iif({question6} allof [d], 4, 0)"}, {"name": "question5Score", "expression": "iif({question5} allof [a], 1, 0) + iif({question5} allof [b], 2, 0) + iif({question5} allof [c], 3, 0) + iif({question5} allof [d], 4, 0)"}, {"name": "question4Score", "expression": "iif({question4} allof [a], 1, 0) + iif({question4} allof [b], 2, 0) + iif({question4} allof [c], 3, 0) + iif({question4} allof [d], 4, 0)"}, {"name": "question3Score", "expression": "iif({question3} allof [a], 1, 0) + iif({question3} allof [b], 2, 0) + iif({question3} allof [c], 3, 0) + iif({question3} allof [d], 4, 0)"}, {"name": "question2Score", "expression": "iif({question2} allof [a], 1, 0) + iif({question2} allof [b], 2, 0) + iif({question2} allof [c], 3, 0) + iif({question2} allof [d], 4, 0)"}, {"name": "question8Score", "expression": "iif({question8} allof [a], 1, 0) + iif({question8} allof [b], 2, 0) + iif({question8} allof [c], 3, 0) + iif({question8} allof [d], 4, 0)"}, {"name": "question9Score", "expression": "iif({question9} allof [a], 1, 0) + iif({question9} allof [b], 2, 0) + iif({question9} allof [c], 3, 0) + iif({question9} allof [d], 4, 0)"}, {"name": "question10Score", "expression": "iif({question10} allof [a], 1, 0) + iif({question10} allof [b], 2, 0) + iif({question10} allof [c], 3, 0) + iif({question10} allof [d], 4, 0)"}, {"name": "question11Score", "expression": "iif({question11} allof [a], 1, 0) + iif({question11} allof [b], 2, 0) + iif({question11} allof [c], 3, 0) + iif({question11} allof [d], 4, 0)"}, {"name": "totalScore_DL", "expression": "({question1Score} + {question2Score} + {question3Score} + {question4Score} + {question5Score} + {question6Score} + {question7Score} + {question8Score} + {question9Score} + {question10Score} + {question11Score})/11"}, {"name": "question12Score", "expression": "iif({question12} allof [a], 1, 0) + iif({question12} allof [b], 2, 0) + iif({question12} allof [c], 3, 0) + iif({question12} allof [d], 4, 0)"}, {"name": "question13Score", "expression": "iif({question13} allof [a], 1, 0) + iif({question13} allof [b], 2, 0) + iif({question13} allof [c], 3, 0) + iif({question13} allof [d], 4, 0)"}, {"name": "question14Score", "expression": "iif({question14} allof [a], 1, 0) + iif({question14} allof [b], 2, 0) + iif({question14} allof [c], 3, 0) + iif({question14} allof [d], 4, 0)"}, {"name": "question15Score", "expression": "iif({question15} allof [a], 1, 0) + iif({question15} allof [b], 2, 0) + iif({question15} allof [c], 3, 0) + iif({question15} allof [d], 4, 0)"}, {"name": "question16Score", "expression": "iif({question16} allof [a], 1, 0) + iif({question16} allof [b], 2, 0) + iif({question16} allof [c], 3, 0) + iif({question16} allof [d], 4, 0)"}, {"name": "question17Score", "expression": "iif({question17} allof [a], 1, 0) + iif({question17} allof [b], 2, 0) + iif({question17} allof [c], 3, 0) + iif({question17} allof [d], 4, 0)"}, {"name": "question18Score", "expression": "iif({question18} allof [a], 1, 0) + iif({question18} allof [b], 2, 0) + iif({question18} allof [c], 3, 0) + iif({question18} allof [d], 4, 0)"}, {"name": "question19Score", "expression": "iif({question19} allof [a], 1, 0) + iif({question19} allof [b], 2, 0) + iif({question19} allof [c], 3, 0) + iif({question19} allof [d], 4, 0)"}, {"name": "question20Score", "expression": "iif({question20} allof [a], 1, 0) + iif({question20} allof [b], 2, 0) + iif({question20} allof [c], 3, 0) + iif({question20} allof [d], 4, 0)"}, {"name": "question21Score", "expression": "iif({question21} allof [a], 1, 0) + iif({question21} allof [b], 2, 0) + iif({question21} allof [c], 3, 0) + iif({question21} allof [d], 4, 0)"}, {"name": "question22Score", "expression": "iif({question22} allof [a], 1, 0) + iif({question22} allof [b], 2, 0) + iif({question22} allof [c], 3, 0) + iif({question22} allof [d], 4, 0)"}, {"name": "question23Score", "expression": "iif({question23} allof [a], 1, 0) + iif({question23} allof [b], 2, 0) + iif({question23} allof [c], 3, 0) + iif({question23} allof [d], 4, 0)"}, {"name": "question24Score", "expression": "iif({question24} allof [a], 1, 0) + iif({question24} allof [b], 2, 0) + iif({question24} allof [c], 3, 0) + iif({question24} allof [d], 4, 0)"}, {"name": "question25Score", "expression": "iif({question25} allof [a], 1, 0) + iif({question25} allof [b], 2, 0) + iif({question25} allof [c], 3, 0) + iif({question25} allof [d], 4, 0)"}, {"name": "question26Score", "expression": "iif({question26} allof [a], 1, 0) + iif({question26} allof [b], 2, 0) + iif({question26} allof [c], 3, 0) + iif({question26} allof [d], 4, 0)"}, {"name": "totalScore_CN", "expression": "({question21Score} + {question12Score} + {question13Score} + {question14Score} + {question15Score} + {question16Score} + {question17Score} + {question18Score} + {question19Score} + {question20Score} + {question22Score} + {question23Score} + {question24Score} + {question25Score} + {question26Score})/15"}, {"name": "question27Score", "expression": "iif({question27} allof [a], 1, 0) + iif({question27} allof [b], 2, 0) + iif({question27} allof [c], 3, 0) + iif({question27} allof [d], 4, 0)"}, {"name": "question28Score", "expression": "iif({question28} alallof [a], 1, 0) + iif({question28} allof [b], 2, 0) + iif({question28} allof [c], 3, 0) + iif({question28} allof [d], 4, 0)"}, {"name": "question29Score", "expression": "iif({question29} allof [a], 1, 0) + iif({question29} allof [b], 2, 0) + iif({question29} allof [c], 3, 0) + iif({question29} allof [d], 4, 0)"}, {"name": "question30Score", "expression": "iif({question30} aallof [a], 1, 0) + iif({question30} allof [b], 2, 0) + iif({question30} allof [c], 3, 0) + iif({question30} allof [d], 4, 0)"}, {"name": "question31Score", "expression": "iif({question31} allof [a], 1, 0) + iif({question31} allof [b], 2, 0) + iif({question31} allof [c], 3, 0) + iif({question31} allof [d], 4, 0)"}, {"name": "question32Score", "expression": "iif({question32} allof [a], 1, 0) + iif({question32} allof [b], 2, 0) + iif({question32} allof [c], 3, 0) + iif({question32} allof [d], 4, 0)"}, {"name": "question33Score", "expression": "iif({question33} allof [a], 1, 0) + iif({question33} allof [b], 2, 0) + iif({question33} allof [c], 3, 0) + iif({question33} allof [d], 4, 0)"}, {"name": "question34Score", "expression": "iif({question34} allof [a], 1, 0) + iif({question34} allof [b], 2, 0) + iif({question34} allof [c], 3, 0) + iif({question34} allof [d], 4, 0)"}, {"name": "question35Score", "expression": "iif({question35} allof [a], 1, 0) + iif({question35} allof [b], 2, 0) + iif({question35} allof [c], 3, 0) + iif({question35} allof [d], 4, 0)"}, {"name": "question36Score", "expression": "iif({question36} allof [a], 1, 0) + iif({question36} allof [b], 2, 0) + iif({question36} allof [c], 3, 0) + iif({question36} allof [d], 4, 0)"}, {"name": "question37Score", "expression": "iif({question37} allof [a], 1, 0) + iif({question37} allof [b], 2, 0) + iif({question37} allof [c], 3, 0) + iif({question37} allof [d], 4, 0)"}, {"name": "question38Score", "expression": "iif({question38} allof [a], 1, 0) + iif({question38} allof [b], 2, 0) + iif({question38} allof [c], 3, 0) + iif({question38} allof [d], 4, 0)"}, {"name": "question39Score", "expression": "iif({question39} allof [a], 1, 0) + iif({question39} allof [b], 2, 0) + iif({question39} allof [c], 3, 0) + iif({question39} allof [d], 4, 0)"}, {"name": "question40Score", "expression": "iif({question40} allof [a], 1, 0) + iif({question40} allof [b], 2, 0) + iif({question40} allof [c], 3, 0) + iif({question40} allof [d], 4, 0)"}, {"name": "question41Score", "expression": "iif({question41} allof [a], 1, 0) + iif({question41} allof [b], 2, 0) + iif({question41} allof [c], 3, 0) + iif({question41} allof [d], 4, 0)"}, {"name": "question42Score", "expression": "iif({question42} allof [a], 1, 0) + iif({question42} allof [b], 2, 0) + iif({question42} allof [c], 3, 0) + iif({question42} allof [d], 4, 0)"}, {"name": "totalScore_CNVHTO", "expression": "({question27Score} + {question28Score} + {question29Score} + {question30Score} + {question31Score} + {question32Score} + {question33Score} + {question34Score} + {question35Score} + {question36Score} + {question37Score} + {question38Score} + {question39Score} + {question40Score} + {question41Score} + {question42Score})/16"}, {"name": "question43Score", "expression": "iif({question43} allof [a], 1, 0) + iif({question43} allof [b], 2, 0) + iif({question43} allof [c], 3, 0) + iif({question43} allof [d], 4, 0)"}, {"name": "question44Score", "expression": "iif({question44} allof [a], 1, 0) + iif({question44} allof [b], 2, 0) + iif({question44} allof [c], 3, 0) + iif({question44} allof [d], 4, 0)"}, {"name": "question45Score", "expression": "iif({question45} allof [a], 1, 0) + iif({question45} allof [b], 2, 0) + iif({question45} allof [c], 3, 0) + iif({question45} allof [d], 4, 0)"}, {"name": "question46Score", "expression": "iif({question46} allof [a], 1, 0) + iif({question46} allof [b], 2, 0) + iif({question46} allof [c], 3, 0) + iif({question46} allof [d], 4, 0)"}, {"name": "question47Score", "expression": "iif({question47} allof [a], 1, 0) + iif({question47} allof [b], 2, 0) + iif({question47} allof [c], 3, 0) + iif({question47} allof [d], 4, 0)"}, {"name": "question48Score", "expression": "iif({question48} allof [a], 1, 0) + iif({question48} allof [b], 2, 0) + iif({question48} allof [c], 3, 0) + iif({question48} allof [d], 4, 0)"}, {"name": "question49Score", "expression": "iif({question49} allof [a], 1, 0) + iif({question49} allof [b], 2, 0) + iif({question49} allof [c], 3, 0) + iif({question49} allof [d], 4, 0)"}, {"name": "question50Score", "expression": "iif({question50} allof [a], 1, 0) + iif({question50} allof [b], 2, 0) + iif({question50} allof [c], 3, 0) + iif({question50} allof [d], 4, 0)"}, {"name": "question51Score", "expression": "iif({question51} allof [a], 1, 0) + iif({question51} allof [b], 2, 0) + iif({question51} allof [c], 3, 0) + iif({question51} allof [d], 4, 0)"}, {"name": "question52Score", "expression": "iif({question52} allof [a], 1, 0) + iif({question52} allof [b], 2, 0) + iif({question52} allof [c], 3, 0) + iif({question52} allof [d], 4, 0)"}, {"name": "question53Score", "expression": "iif({question53} allof [a], 1, 0) + iif({question53} allof [b], 2, 0) + iif({question53} allof [c], 3, 0) + iif({question53} allof [d], 4, 0)"}, {"name": "question54Score", "expression": "iif({question54} allof [a], 1, 0) + iif({question54} allof [b], 2, 0) + iif({question54} allof [c], 3, 0) + iif({question54} allof [d], 4, 0)"}, {"name": "question55Score", "expression": "iif({question55} allof [a], 1, 0) + iif({question55} allof [b], 2, 0) + iif({question55} allof [c], 3, 0) + iif({question55} allof [d], 4, 0)"}, {"name": "question56Score", "expression": "iif({question56} allof [a], 1, 0) + iif({question56} allof [b], 2, 0) + iif({question56} allof [c], 3, 0) + iif({question56} allof [d], 4, 0)"}, {"name": "question57Score", "expression": "iif({question57} allof [a], 1, 0) + iif({question57} allof [b], 2, 0) + iif({question57} allof [c], 3, 0) + iif({question57} allof [d], 4, 0)"}, {"name": "question58Score", "expression": "iif({question58} allof [a], 1, 0) + iif({question58} allof [b], 2, 0) + iif({question58} allof [c], 3, 0) + iif({question58} allof [d], 4, 0)"}, {"name": "question59Score", "expression": "iif({question59} allof [a], 1, 0) + iif({question59} allof [b], 2, 0) + iif({question59} allof [c], 3, 0) + iif({question59} allof [d], 4, 0)"}, {"name": "question60Score", "expression": "iif({question60} allof [a], 1, 0) + iif({question60} allof [b], 2, 0) + iif({question60} allof [c], 3, 0) + iif({question60} allof [d], 4, 0)"}, {"name": "question61Score", "expression": "iif({question61} allof [a], 1, 0) + iif({question61} allof [b], 2, 0) + iif({question61} allof [c], 3, 0) + iif({question61} allof [d], 4, 0)"}, {"name": "question62Score", "expression": "iif({question62} allof [a], 1, 0) + iif({question62} allof [b], 2, 0) + iif({question62} allof [c], 3, 0) + iif({question62} allof [d], 4, 0)"}, {"name": "totalScore_QTVVH", "expression": "({question43Score} + {question44Score} + {question45Score} + {question46Score} + {question47Score} + {question48Score} + {question49Score} + {question50Score} + {question51Score} + {question52Score} + {question53Score} + {question54Score} + {question55Score} + {question56Score} + {question57Score} + {question58Score} + {question59Score} + {question60Score} + {question61Score} + {question62Score})/20"}, {"name": "question63Score", "expression": "iif({question63} allof [a], 1, 0) + iif({question63} allof [b], 2, 0) + iif({question63} allof [c], 3, 0) + iif({question63} allof [d], 4, 0)"}, {"name": "question64Score", "expression": "iif({question64} allof [a], 1, 0) + iif({question64} allof [b], 2, 0) + iif({question64} allof [c], 3, 0) + iif({question64} allof [d], 4, 0)"}, {"name": "question65Score", "expression": "iif({question65} allof [a], 1, 0) + iif({question65} allof [b], 2, 0) + iif({question65} allof [c], 3, 0) + iif({question65} allof [d], 4, 0)"}, {"name": "question66Score", "expression": "iif({question66} allof [a], 1, 0) + iif({question66} allof [b], 2, 0) + iif({question66} allof [c], 3, 0) + iif({question66} allof [d], 4, 0)"}, {"name": "question67Score", "expression": "iif({question67} allof [a], 1, 0) + iif({question67} allof [b], 2, 0) + iif({question67} allof [c], 3, 0) + iif({question67} allof [d], 4, 0)"}, {"name": "question68Score", "expression": "iif({question68} allof [a], 1, 0) + iif({question68} allof [b], 2, 0) + iif({question68} allof [c], 3, 0) + iif({question68} allof [d], 4, 0)"}, {"name": "question69Score", "expression": "iif({question69} allof [a], 1, 0) + iif({question69} allof [b], 2, 0) + iif({question69} allof [c], 3, 0) + iif({question69} allof [d], 4, 0)"}, {"name": "question70Score", "expression": "iif({question70} allof [a], 1, 0) + iif({question70} allof [b], 2, 0) + iif({question70} allof [c], 3, 0) + iif({question70} allof [d], 4, 0)"}, {"name": "question71Score", "expression": "iif({question71} allof [a], 1, 0) + iif({question71} allof [b], 2, 0) + iif({question71} allof [c], 3, 0) + iif({question71} allof [d], 4, 0)"}, {"name": "question72Score", "expression": "iif({question72} allof [a], 1, 0) + iif({question72} allof [b], 2, 0) + iif({question72} allof [c], 3, 0) + iif({question72} allof [d], 4, 0)"}, {"name": "question73Score", "expression": "iif({question73} allof [a], 1, 0) + iif({question73} allof [b], 2, 0) + iif({question73} allof [c], 3, 0) + iif({question73} allof [d], 4, 0)"}, {"name": "question74Score", "expression": "iif({question74} allof [a], 1, 0) + iif({question74} allof [b], 2, 0) + iif({question74} allof [c], 3, 0) + iif({question74} allof [d], 4, 0)"}, {"name": "question75Score", "expression": "iif({question75} allof [a], 1, 0) + iif({question75} allof [b], 2, 0) + iif({question75} allof [c], 3, 0) + iif({question75} allof [d], 4, 0)"}, {"name": "question76Score", "expression": "iif({question76} allof [a], 1, 0) + iif({question76} allof [b], 2, 0) + iif({question76} allof [c], 3, 0) + iif({question76} allof [d], 4, 0)"}, {"name": "question77Score", "expression": "iif({question77} allof [a], 1, 0) + iif({question77} allof [b], 2, 0) + iif({question77} allof [c], 3, 0) + iif({question77} allof [d], 4, 0)"}, {"name": "question78Score", "expression": "iif({question78} allof [a], 1, 0) + iif({question78} allof [b], 2, 0) + iif({question78} allof [c], 3, 0) + iif({question78} allof [d], 4, 0)"}, {"name": "question79Score", "expression": "iif({question79} allof [a], 1, 0) + iif({question79} allof [b], 2, 0) + iif({question79} allof [c], 3, 0) + iif({question79} allof [d], 4, 0)"}, {"name": "question80Score", "expression": "iif({question80} allof [a], 1, 0) + iif({question80} allof [b], 2, 0) + iif({question80} allof [c], 3, 0) + iif({question80} allof [d], 4, 0)"}, {"name": "question81Score", "expression": "iif({question81} allof [a], 1, 0) + iif({question81} allof [b], 2, 0) + iif({question81} allof [c], 3, 0) + iif({question81} allof [d], 4, 0)"}, {"name": "question82Score", "expression": "iif({question82} allof [a], 1, 0) + iif({question82} allof [b], 2, 0) + iif({question82} allof [c], 3, 0) + iif({question82} allof [d], 4, 0)"}, {"name": "totalScore_CLVLD", "expression": "({question63Score} + {question64Score} + {question65Score} + {question66Score} + {question67Score} + {question68Score} + {question69Score} + {question70Score} + {question71Score} + {question72Score} + {question73Score} + {question74Score} + {question75Score} + {question76Score} + {question77Score} + {question78Score} + {question79Score} + {question80Score} + {question81Score} + {question82Score})/20"}], "cookieName": "1", "headerView": "advanced"}